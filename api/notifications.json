{"notifications": [{"id": "notif-001", "type": "order", "title": "New order received", "message": "Order #12345 has been placed by <PERSON>", "timestamp": "2024-01-15T10:30:00Z", "read": false, "priority": "high", "icon": "shopping-cart", "actionUrl": "/orders/12345", "metadata": {"orderId": "12345", "customerName": "<PERSON>", "amount": 299.99}}, {"id": "notif-002", "type": "payment", "title": "Payment processed", "message": "Payment of $299.99 has been successfully processed", "timestamp": "2024-01-15T10:25:00Z", "read": false, "priority": "medium", "icon": "credit-card", "actionUrl": "/payments/pay-789", "metadata": {"paymentId": "pay-789", "amount": 299.99, "method": "credit_card"}}, {"id": "notif-003", "type": "system", "title": "System maintenance scheduled", "message": "Scheduled maintenance will occur tonight at 2:00 AM", "timestamp": "2024-01-15T09:00:00Z", "read": true, "priority": "low", "icon": "settings", "actionUrl": "/system/maintenance", "metadata": {"maintenanceWindow": "2024-01-16T02:00:00Z", "duration": "2 hours"}}, {"id": "notif-004", "type": "inventory", "title": "Low stock alert", "message": "Product 'Wireless Headphones' is running low on stock", "timestamp": "2024-01-15T08:45:00Z", "read": false, "priority": "high", "icon": "alert-triangle", "actionUrl": "/inventory/prod-456", "metadata": {"productId": "prod-456", "productName": "Wireless Headphones", "currentStock": 5, "threshold": 10}}, {"id": "notif-005", "type": "user", "title": "New user registration", "message": "<PERSON> has created a new account", "timestamp": "2024-01-15T08:30:00Z", "read": true, "priority": "low", "icon": "user-plus", "actionUrl": "/users/user-123", "metadata": {"userId": "user-123", "userName": "<PERSON>", "email": "<EMAIL>"}}, {"id": "notif-006", "type": "security", "title": "Failed login attempt", "message": "Multiple failed login attempts detected from IP *************", "timestamp": "2024-01-15T07:15:00Z", "read": false, "priority": "high", "icon": "shield-alert", "actionUrl": "/security/logs", "metadata": {"ipAddress": "*************", "attempts": 5, "timeWindow": "10 minutes"}}], "summary": {"total": 6, "unread": 4, "byPriority": {"high": 3, "medium": 1, "low": 2}, "byType": {"order": 1, "payment": 1, "system": 1, "inventory": 1, "user": 1, "security": 1}}}