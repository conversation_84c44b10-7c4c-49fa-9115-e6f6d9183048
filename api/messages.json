{"messages": [{"id": "msg-001", "from": {"id": "user-456", "name": "<PERSON>", "email": "<EMAIL>", "avatar": "/avatars/john-doe.jpg"}, "subject": "Project Update Required", "preview": "Hey, how's the project going? We need to discuss the latest requirements...", "content": "Hey, how's the project going? We need to discuss the latest requirements and timeline adjustments. Can we schedule a meeting this week?", "timestamp": "2024-01-15T11:30:00Z", "read": false, "starred": false, "priority": "normal", "thread": "thread-001", "attachments": [], "labels": ["work", "urgent"]}, {"id": "msg-002", "from": {"id": "user-789", "name": "<PERSON>", "email": "<EMAIL>", "avatar": "/avatars/jane-smith.jpg"}, "subject": "Meeting Invitation", "preview": "Can we schedule a meeting for tomorrow? I'd like to discuss the new features...", "content": "Can we schedule a meeting for tomorrow? I'd like to discuss the new features we're planning to implement. Let me know your availability.", "timestamp": "2024-01-15T10:45:00Z", "read": false, "starred": true, "priority": "high", "thread": "thread-002", "attachments": [{"name": "meeting-agenda.pdf", "size": "245KB", "type": "application/pdf"}], "labels": ["meeting", "important"]}, {"id": "msg-003", "from": {"id": "user-321", "name": "<PERSON>", "email": "<EMAIL>", "avatar": "/avatars/mike-johnson.jpg"}, "subject": "Code Review Request", "preview": "Could you please review the latest pull request? It includes the new dashboard components...", "content": "Could you please review the latest pull request? It includes the new dashboard components we discussed. The changes are ready for your feedback.", "timestamp": "2024-01-15T09:20:00Z", "read": true, "starred": false, "priority": "normal", "thread": "thread-003", "attachments": [], "labels": ["code-review", "development"]}, {"id": "msg-004", "from": {"id": "user-654", "name": "<PERSON>", "email": "<EMAIL>", "avatar": "/avatars/sarah-wilson.jpg"}, "subject": "Budget Approval Needed", "preview": "The Q1 budget proposal is ready for your review and approval...", "content": "The Q1 budget proposal is ready for your review and approval. Please check the attached spreadsheet and let me know if you have any questions.", "timestamp": "2024-01-15T08:15:00Z", "read": true, "starred": false, "priority": "high", "thread": "thread-004", "attachments": [{"name": "Q1-budget-proposal.xlsx", "size": "1.2MB", "type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}], "labels": ["finance", "approval-needed"]}, {"id": "msg-005", "from": {"id": "user-987", "name": "<PERSON>", "email": "<EMAIL>", "avatar": "/avatars/david-brown.jpg"}, "subject": "Server Maintenance Notice", "preview": "Scheduled server maintenance will take place this weekend...", "content": "Scheduled server maintenance will take place this weekend from 2:00 AM to 6:00 AM. All services will be temporarily unavailable during this time.", "timestamp": "2024-01-14T16:30:00Z", "read": true, "starred": false, "priority": "normal", "thread": "thread-005", "attachments": [], "labels": ["maintenance", "system"]}], "summary": {"total": 5, "unread": 2, "starred": 1, "byPriority": {"high": 2, "normal": 3, "low": 0}, "threads": 5}, "folders": [{"id": "inbox", "name": "Inbox", "count": 5, "unread": 2}, {"id": "sent", "name": "<PERSON><PERSON>", "count": 12, "unread": 0}, {"id": "drafts", "name": "Drafts", "count": 3, "unread": 0}, {"id": "trash", "name": "Trash", "count": 8, "unread": 0}]}