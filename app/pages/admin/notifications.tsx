import type { Route } from "./+types/notifications";
import { AppLayout } from "~/components/layouts/app-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import {
  Bell,
  Mail,
  AlertCircle,
  CheckCircle,
  Info,
  X,
  Check,
  Trash2,
  Settings,
  Filter
} from "lucide-react";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Notifications - Admin Panel" },
    { name: "description", content: "Manage notifications and alerts" },
  ];
}

export default function NotificationsPage() {
  const breadcrumbItems = [
    { title: "Home", href: "/" },
    { title: "Notifications", isCurrentPage: true }
  ];

  const notifications = [
    {
      id: 1,
      title: "New user registration",
      message: "<PERSON> has registered for an account and is awaiting approval.",
      type: "info",
      timestamp: "2 minutes ago",
      read: false,
      category: "Users"
    },
    {
      id: 2,
      title: "Payment processed successfully",
      message: "Payment of $299.99 has been processed for order #12345.",
      type: "success",
      timestamp: "5 minutes ago",
      read: false,
      category: "Payments"
    },
    {
      id: 3,
      title: "System maintenance scheduled",
      message: "Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM EST.",
      type: "warning",
      timestamp: "1 hour ago",
      read: true,
      category: "System"
    },
    {
      id: 4,
      title: "Security alert",
      message: "Unusual login activity detected from IP address *************.",
      type: "error",
      timestamp: "2 hours ago",
      read: false,
      category: "Security"
    },
    {
      id: 5,
      title: "Backup completed",
      message: "Daily backup has been completed successfully. All data is secure.",
      type: "success",
      timestamp: "3 hours ago",
      read: true,
      category: "System"
    },
    {
      id: 6,
      title: "Low storage warning",
      message: "Server storage is at 85% capacity. Consider upgrading your plan.",
      type: "warning",
      timestamp: "6 hours ago",
      read: false,
      category: "System"
    },
    {
      id: 7,
      title: "New feature released",
      message: "PWA functionality has been added to your dashboard. Check it out!",
      type: "info",
      timestamp: "1 day ago",
      read: true,
      category: "Updates"
    },
    {
      id: 8,
      title: "Invoice generated",
      message: "Monthly invoice #INV-2024-001 has been generated and sent.",
      type: "info",
      timestamp: "2 days ago",
      read: true,
      category: "Billing"
    }
  ];

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "success":
        return CheckCircle;
      case "warning":
        return AlertCircle;
      case "error":
        return X;
      default:
        return Info;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case "success":
        return "text-green-600";
      case "warning":
        return "text-yellow-600";
      case "error":
        return "text-red-600";
      default:
        return "text-blue-600";
    }
  };

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "success":
        return <Badge className="bg-green-500">Success</Badge>;
      case "warning":
        return <Badge className="bg-yellow-500">Warning</Badge>;
      case "error":
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge className="bg-blue-500">Info</Badge>;
    }
  };

  const getCategoryBadge = (category: string) => {
    const colors: Record<string, string> = {
      "Users": "bg-purple-500",
      "Payments": "bg-green-500",
      "System": "bg-blue-500",
      "Security": "bg-red-500",
      "Updates": "bg-indigo-500",
      "Billing": "bg-orange-500"
    };
    return <Badge variant="outline" className={colors[category]}>{category}</Badge>;
  };

  const unreadCount = notifications.filter(n => !n.read).length;
  const categories = [...new Set(notifications.map(n => n.category))];

  return (
    <AppLayout
      title="Notifications"
      breadcrumbItems={breadcrumbItems}
      className="gap-6 p-6 pt-0"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
          <p className="text-muted-foreground">
            Manage your notifications and stay updated with important events.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline">
            <Check className="mr-2 h-4 w-4" />
            Mark All Read
          </Button>
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Notifications</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{notifications.length}</div>
            <p className="text-xs text-muted-foreground">
              All time notifications
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unread</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{unreadCount}</div>
            <p className="text-xs text-muted-foreground">
              Require attention
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <Filter className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categories.length}</div>
            <p className="text-xs text-muted-foreground">
              Different types
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">
              New notifications
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Notifications List */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Notifications</CardTitle>
          <CardDescription>
            Latest notifications and system alerts.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {notifications.map((notification) => {
              const Icon = getNotificationIcon(notification.type);
              const iconColor = getNotificationColor(notification.type);
              
              return (
                <div 
                  key={notification.id} 
                  className={`p-4 border rounded-lg transition-colors ${
                    !notification.read ? 'bg-blue-50 border-blue-200' : 'hover:bg-muted/50'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <Icon className={`h-5 w-5 mt-0.5 ${iconColor}`} />
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h4 className={`font-medium ${!notification.read ? 'font-semibold' : ''}`}>
                            {notification.title}
                          </h4>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full" />
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {notification.message}
                        </p>
                        <div className="flex items-center gap-2">
                          {getTypeBadge(notification.type)}
                          {getCategoryBadge(notification.category)}
                          <span className="text-xs text-muted-foreground">
                            {notification.timestamp}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-1">
                      {!notification.read && (
                        <Button size="sm" variant="ghost">
                          <Check className="h-4 w-4" />
                        </Button>
                      )}
                      <Button size="sm" variant="ghost">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </AppLayout>
  );
}
