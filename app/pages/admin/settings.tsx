import type { Route } from "./+types/settings";
import { AppLayout } from "~/components/layouts/app-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { 
  Settings, 
  Shield, 
  Bell, 
  Palette, 
  Database, 
  Mail, 
  Globe, 
  Lock,
  Save,
  RefreshCw
} from "lucide-react";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Settings - Admin Panel" },
    { name: "description", content: "Application settings and configuration" },
  ];
}

export default function SettingsPage() {
  const breadcrumbItems = [
    { title: "Home", href: "/" },
    { title: "Settings", isCurrentPage: true }
  ];

  const settingsCategories = [
    {
      id: "general",
      title: "General Settings",
      description: "Basic application configuration",
      icon: Settings,
      settings: [
        { key: "app_name", label: "Application Name", value: "Admin Dashboard", type: "text" },
        { key: "app_description", label: "Description", value: "Modern admin dashboard", type: "text" },
        { key: "timezone", label: "Default Timezone", value: "UTC", type: "select" },
        { key: "language", label: "Default Language", value: "English", type: "select" }
      ]
    },
    {
      id: "security",
      title: "Security Settings",
      description: "Authentication and security configuration",
      icon: Shield,
      settings: [
        { key: "two_factor", label: "Two-Factor Authentication", value: "Enabled", type: "toggle" },
        { key: "session_timeout", label: "Session Timeout (minutes)", value: "30", type: "number" },
        { key: "password_policy", label: "Strong Password Policy", value: "Enabled", type: "toggle" },
        { key: "login_attempts", label: "Max Login Attempts", value: "5", type: "number" }
      ]
    },
    {
      id: "notifications",
      title: "Notification Settings",
      description: "Email and push notification preferences",
      icon: Bell,
      settings: [
        { key: "email_notifications", label: "Email Notifications", value: "Enabled", type: "toggle" },
        { key: "push_notifications", label: "Push Notifications", value: "Enabled", type: "toggle" },
        { key: "notification_frequency", label: "Frequency", value: "Immediate", type: "select" },
        { key: "digest_emails", label: "Daily Digest", value: "Enabled", type: "toggle" }
      ]
    },
    {
      id: "appearance",
      title: "Appearance",
      description: "Theme and UI customization",
      icon: Palette,
      settings: [
        { key: "theme", label: "Theme", value: "Light", type: "select" },
        { key: "sidebar_collapsed", label: "Sidebar Collapsed", value: "Disabled", type: "toggle" },
        { key: "compact_mode", label: "Compact Mode", value: "Disabled", type: "toggle" },
        { key: "animations", label: "Animations", value: "Enabled", type: "toggle" }
      ]
    }
  ];

  const systemStatus = [
    { service: "Database", status: "healthy", uptime: "99.9%", lastCheck: "2 min ago" },
    { service: "Cache Server", status: "healthy", uptime: "99.8%", lastCheck: "1 min ago" },
    { service: "Email Service", status: "warning", uptime: "98.5%", lastCheck: "5 min ago" },
    { service: "File Storage", status: "healthy", uptime: "99.9%", lastCheck: "3 min ago" },
    { service: "API Gateway", status: "healthy", uptime: "99.7%", lastCheck: "1 min ago" }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "healthy":
        return <Badge className="bg-green-500">Healthy</Badge>;
      case "warning":
        return <Badge className="bg-yellow-500">Warning</Badge>;
      case "error":
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <AppLayout
      title="Settings"
      breadcrumbItems={breadcrumbItems}
      className="gap-6 p-6 pt-0"
    >
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Manage your application settings and system configuration.
        </p>
      </div>

      {/* Settings Categories */}
      <div className="grid gap-6 md:grid-cols-2">
        {settingsCategories.map((category) => {
          const Icon = category.icon;
          return (
            <Card key={category.id}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Icon className="h-5 w-5" />
                  {category.title}
                </CardTitle>
                <CardDescription>{category.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {category.settings.map((setting) => (
                  <div key={setting.key} className="flex items-center justify-between">
                    <div className="space-y-1">
                      <label className="text-sm font-medium">{setting.label}</label>
                    </div>
                    <div className="w-32">
                      {setting.type === "text" || setting.type === "number" ? (
                        <Input 
                          type={setting.type} 
                          defaultValue={setting.value} 
                          className="text-right"
                        />
                      ) : setting.type === "toggle" ? (
                        <Badge variant={setting.value === "Enabled" ? "default" : "secondary"}>
                          {setting.value}
                        </Badge>
                      ) : (
                        <Badge variant="outline">{setting.value}</Badge>
                      )}
                    </div>
                  </div>
                ))}
                <div className="pt-4 flex gap-2">
                  <Button size="sm">
                    <Save className="mr-2 h-4 w-4" />
                    Save
                  </Button>
                  <Button variant="outline" size="sm">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reset
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            System Status
          </CardTitle>
          <CardDescription>
            Current status of all system services and components.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {systemStatus.map((service) => (
              <div key={service.service} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="space-y-1">
                    <div className="font-medium">{service.service}</div>
                    <div className="text-sm text-muted-foreground">
                      Uptime: {service.uptime} • Last check: {service.lastCheck}
                    </div>
                  </div>
                </div>
                {getStatusBadge(service.status)}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </AppLayout>
  );
}
