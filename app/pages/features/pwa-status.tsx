import { useEffect, useState, useCallback } from "react"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { Download, AlertCircle, CheckCircle2, Info, BarChart2 } from "lucide-react"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "~/components/ui/tooltip"
import { Badge } from "~/components/ui/badge"

type BeforeInstallPromptEvent = Event & {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: "accepted" | "dismissed"; platform: string }>
}

interface PwaStatusProps {
  onAnalyzeClick?: () => void
}

export default function PwaStatus({ onAnalyzeClick }: PwaStatusProps) {
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [isPWA, setIsPWA] = useState(false)
  const [isInstallable, setIsInstallable] = useState(false)
  const [errors, setErrors] = useState<string[]>([])
  const [manifestChecked, setManifestChecked] = useState(false)

  // Memoize the PWA requirements check function
  const checkPWARequirements = useCallback(async () => {
    const newErrors: string[] = []

    // Check for HTTPS
    if (
      window.location.protocol !== "https:" &&
      window.location.hostname !== "localhost" &&
      window.location.hostname !== "127.0.0.1"
    ) {
      newErrors.push("HTTPS is required")
    }

    // Check for service worker support
    if (!("serviceWorker" in navigator)) {
      newErrors.push("Service Worker not supported")
    }

    // Check for web app manifest
    try {
      const manifestLinks = document.querySelectorAll('link[rel="manifest"]')
      if (manifestLinks.length === 0) {
        newErrors.push("Web App Manifest link not found in HTML")
      } else {
        // Try to fetch the manifest to verify it exists and is valid
        const manifestHref = manifestLinks[0].getAttribute("href")
        if (manifestHref) {
          try {
            const response = await fetch(manifestHref)
            if (!response.ok) {
              newErrors.push(`Manifest file not found (${response.status})`)
            } else {
              try {
                const manifestData = await response.json()
                // Check for required fields
                if (!manifestData.name) newErrors.push("Manifest missing 'name'")
                if (!manifestData.short_name) newErrors.push("Manifest missing 'short_name'")
                if (!manifestData.icons || manifestData.icons.length === 0) newErrors.push("Manifest missing 'icons'")
                if (!manifestData.start_url) newErrors.push("Manifest missing 'start_url'")
                if (!manifestData.display) newErrors.push("Manifest missing 'display'")

                console.log("Manifest validation complete:", manifestData)
              } catch (e) {
                newErrors.push("Manifest is not valid JSON")
              }
            }
          } catch (e) {
            newErrors.push("Failed to fetch manifest file")
          }
        } else {
          newErrors.push("Manifest link has no href")
        }
      }
    } catch (e) {
      newErrors.push("Error checking manifest: " + (e as Error).message)
    }

    setErrors(newErrors)
    setManifestChecked(true)
  }, [])

  useEffect(() => {
    // Check if we're in browser environment
    if (typeof window === 'undefined') return

    // Check if running as installed PWA
    if (window.matchMedia("(display-mode: standalone)").matches || (window.navigator as any).standalone === true) {
      setIsPWA(true)
    }

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setInstallPrompt(e as BeforeInstallPromptEvent)
      setIsInstallable(true)
    }

    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt)

    // Run PWA requirements check
    checkPWARequirements()

    return () => {
      window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt)
    }
  }, [checkPWARequirements])

  const handleInstall = async () => {
    if (!installPrompt) return

    // Show the install prompt
    await installPrompt.prompt()

    // Wait for the user to respond to the prompt
    const choiceResult = await installPrompt.userChoice

    if (choiceResult.outcome === "accepted") {
      console.log("User accepted the install prompt")
      setInstallPrompt(null)
    } else {
      console.log("User dismissed the install prompt")
    }
  }

  if (isPWA) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1 border-green-500/30">
              <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
              <span className="text-xs">Installed</span>
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p>Running as installed PWA</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  if (errors.length > 0) {
    return (
      <div className="flex items-center gap-1">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="outline" className="flex items-center gap-1 px-3 py-1 border-amber-500/30">
                <AlertCircle className="h-3.5 w-3.5 text-amber-500" />
                <span className="text-xs">Not installable</span>
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1 max-w-xs">
                <p className="font-semibold">Installation issues:</p>
                <ul className="text-xs list-disc pl-4">
                  {errors.slice(0, 3).map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                  {errors.length > 3 && <li>...and {errors.length - 3} more issues</li>}
                </ul>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        {onAnalyzeClick && (
          <Button variant="ghost" size="icon" onClick={onAnalyzeClick} className="h-7 w-7">
            <BarChart2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    )
  }

  if (isInstallable) {
    return (
      <div className="flex items-center gap-1">
        <Button variant="outline" size="sm" className="flex items-center gap-1" onClick={handleInstall}>
          <Download className="h-4 w-4" />
          <span>Install</span>
        </Button>
        {onAnalyzeClick && (
          <Button variant="ghost" size="icon" onClick={onAnalyzeClick} className="h-7 w-7">
            <BarChart2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className="flex items-center gap-1">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              {manifestChecked ? (
                <>
                  <Info className="h-3.5 w-3.5 text-blue-500" />
                  <span className="text-xs">Ready for install</span>
                </>
              ) : (
                <span className="text-xs">Checking...</span>
              )}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            {manifestChecked ? <p>PWA is ready, waiting for browser criteria</p> : <p>Checking PWA compatibility...</p>}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      {onAnalyzeClick && (
        <Button variant="ghost" size="icon" onClick={onAnalyzeClick} className="h-7 w-7">
          <BarChart2 className="h-4 w-4" />
        </Button>
      )}
    </div>
  )
}
