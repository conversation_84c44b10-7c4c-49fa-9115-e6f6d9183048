import type { Route } from "./+types/pwa-demo";
import { useState, useCallback, useEffect } from "react"
import { AppLayout } from "~/components/layouts/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { But<PERSON> } from "~/components/ui/button"
import { Badge } from "~/components/ui/badge"
import { Alert, AlertDescription } from "~/components/ui/alert"
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "~/components/ui/tabs"
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Smartphone, 
  Monitor, 
  Wifi, 
  WifiOff,
  Download,
  Bell,
  Shield,
  Zap,
  Globe,
  Settings,
  TestTube,
  BookOpen,
  Star,
  Info
} from "lucide-react"
import { cn } from "~/lib/utils"
import {
  PWAInstaller,
  ServiceWorkerManager,
  PWAStatus,
  PWAEventLogger,
  PWATest,
  NotificationTest
} from "~/components/base/pwa"
import type { ServiceWorkerStatus, PWAEvent } from "~/components/base/pwa"

export function meta({}: Route.MetaArgs) {
  return [
    { title: "PWA Demo - Admin Panel" },
    { name: "description", content: "Comprehensive Progressive Web App demonstration and testing suite" },
  ];
}

interface PWARequirement {
  id: string
  name: string
  description: string
  status: 'pass' | 'fail' | 'warning' | 'pending'
  details: string
  icon: React.ComponentType<any>
  category: 'core' | 'feature' | 'enhancement'
}

export default function PWADemoPage() {
  const [serviceWorkerStatus, setServiceWorkerStatus] = useState<ServiceWorkerStatus | undefined>()
  const [eventCount, setEventCount] = useState(0)
  const [requirements, setRequirements] = useState<PWARequirement[]>([])
  const [isOnline, setIsOnline] = useState(true)
  const [manifestData, setManifestData] = useState<any>(null)

  const breadcrumbItems = [
    { title: "Home", href: "/" },
    { title: "PWA Features", href: "/pwa" },
    { title: "PWA Demo", isCurrentPage: true }
  ];

  const handleServiceWorkerStateChange = useCallback((status: ServiceWorkerStatus) => {
    setServiceWorkerStatus(status)
  }, [])

  const handleEventLogged = useCallback((event: PWAEvent) => {
    setEventCount(prev => prev + 1)
  }, [])

  // Load manifest data
  useEffect(() => {
    if (typeof window !== 'undefined') {
      fetch('/manifest.json')
        .then(response => response.json())
        .then(data => setManifestData(data))
        .catch(error => console.error('Failed to load manifest:', error))
    }
  }, [])

  // Monitor online status
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const updateOnlineStatus = () => setIsOnline(navigator.onLine)
      
      setIsOnline(navigator.onLine)
      window.addEventListener('online', updateOnlineStatus)
      window.addEventListener('offline', updateOnlineStatus)
      
      return () => {
        window.removeEventListener('online', updateOnlineStatus)
        window.removeEventListener('offline', updateOnlineStatus)
      }
    }
  }, [])

  // Initialize PWA requirements check
  useEffect(() => {
    if (typeof window === 'undefined') return

    const checkRequirements = async () => {
      const reqs: PWARequirement[] = [
        // Core Requirements
        {
          id: 'manifest',
          name: 'Web App Manifest',
          description: 'Provides metadata about the web application',
          status: manifestData ? 'pass' : 'pending',
          details: manifestData ? `Name: ${manifestData.name}` : 'Loading manifest...',
          icon: BookOpen,
          category: 'core'
        },
        {
          id: 'service-worker',
          name: 'Service Worker',
          description: 'Enables offline functionality and caching',
          status: 'serviceWorker' in navigator ? 'pass' : 'fail',
          details: 'serviceWorker' in navigator ? 'Service Worker API supported' : 'Service Worker not supported',
          icon: Settings,
          category: 'core'
        },
        {
          id: 'https',
          name: 'Secure Context (HTTPS)',
          description: 'Required for PWA features to work',
          status: location.protocol === 'https:' || location.hostname === 'localhost' ? 'pass' : 'fail',
          details: `Protocol: ${location.protocol}`,
          icon: Shield,
          category: 'core'
        },
        {
          id: 'responsive',
          name: 'Responsive Design',
          description: 'Works on mobile and desktop devices',
          status: 'pass',
          details: 'Mobile-first responsive design implemented',
          icon: Smartphone,
          category: 'core'
        },
        // Features
        {
          id: 'installable',
          name: 'Installable',
          description: 'Can be installed on user devices',
          status: 'pending',
          details: 'Checking installation capability...',
          icon: Download,
          category: 'feature'
        },
        {
          id: 'offline',
          name: 'Offline Functionality',
          description: 'Works without internet connection',
          status: 'pending',
          details: 'Checking offline capabilities...',
          icon: WifiOff,
          category: 'feature'
        },
        {
          id: 'notifications',
          name: 'Push Notifications',
          description: 'Can send notifications to users',
          status: 'Notification' in window ? 'pass' : 'fail',
          details: 'Notification' in window ? 'Notification API supported' : 'Notifications not supported',
          icon: Bell,
          category: 'feature'
        },
        // Enhancements
        {
          id: 'background-sync',
          name: 'Background Sync',
          description: 'Synchronizes data when back online',
          status: 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype ? 'pass' : 'warning',
          details: 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype ? 'Background Sync supported' : 'Background Sync not supported',
          icon: Zap,
          category: 'enhancement'
        },
        {
          id: 'app-shell',
          name: 'App Shell Architecture',
          description: 'Fast loading application shell',
          status: 'pass',
          details: 'App shell pattern implemented',
          icon: Globe,
          category: 'enhancement'
        }
      ]

      setRequirements(reqs)
    }

    checkRequirements()
  }, [manifestData])

  const getStatusColor = (status: PWARequirement['status']) => {
    switch (status) {
      case 'pass': return 'text-green-600'
      case 'fail': return 'text-red-600'
      case 'warning': return 'text-yellow-600'
      case 'pending': return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: PWARequirement['status']) => {
    switch (status) {
      case 'pass': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'fail': return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'warning': return <AlertCircle className="h-4 w-4 text-yellow-600" />
      case 'pending': return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusBadge = (status: PWARequirement['status']) => {
    switch (status) {
      case 'pass': return <Badge variant="default" className="bg-green-600">Pass</Badge>
      case 'fail': return <Badge variant="destructive">Fail</Badge>
      case 'warning': return <Badge variant="secondary" className="bg-yellow-600 text-white">Warning</Badge>
      case 'pending': return <Badge variant="secondary">Pending</Badge>
    }
  }

  const coreRequirements = requirements.filter(req => req.category === 'core')
  const featureRequirements = requirements.filter(req => req.category === 'feature')
  const enhancementRequirements = requirements.filter(req => req.category === 'enhancement')

  const overallScore = requirements.length > 0 
    ? Math.round((requirements.filter(req => req.status === 'pass').length / requirements.length) * 100)
    : 0

  return (
    <AppLayout
      title="PWA Demonstration"
      breadcrumbItems={breadcrumbItems}
      className="gap-6 p-6 pt-0"
    >
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">PWA Demonstration</h1>
            <p className="text-muted-foreground">
              Comprehensive Progressive Web App showcase and testing suite
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-2xl font-bold text-green-600">{overallScore}%</div>
              <div className="text-sm text-muted-foreground">PWA Score</div>
            </div>
            <div className="flex items-center gap-2">
              {isOnline ? (
                <Wifi className="h-5 w-5 text-green-600" />
              ) : (
                <WifiOff className="h-5 w-5 text-red-600" />
              )}
              <span className="text-sm">{isOnline ? 'Online' : 'Offline'}</span>
            </div>
          </div>
        </div>

        {/* Quick Status Overview */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-500" />
                <div>
                  <div className="font-medium">Core Features</div>
                  <div className="text-sm text-muted-foreground">
                    {coreRequirements.filter(req => req.status === 'pass').length}/{coreRequirements.length} passed
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-blue-500" />
                <div>
                  <div className="font-medium">PWA Features</div>
                  <div className="text-sm text-muted-foreground">
                    {featureRequirements.filter(req => req.status === 'pass').length}/{featureRequirements.length} available
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-purple-500" />
                <div>
                  <div className="font-medium">Service Worker</div>
                  <div className="text-sm text-muted-foreground">
                    {serviceWorkerStatus?.state || 'Unknown'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TestTube className="h-5 w-5 text-green-500" />
                <div>
                  <div className="font-medium">Events Logged</div>
                  <div className="text-sm text-muted-foreground">
                    {eventCount} events
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="requirements">Requirements</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="testing">Testing</TabsTrigger>
          <TabsTrigger value="manifest">Manifest</TabsTrigger>
          <TabsTrigger value="docs">Documentation</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* PWA Status Overview */}
          <div className="grid gap-6 md:grid-cols-2">
            <PWAStatus serviceWorkerStatus={serviceWorkerStatus} />
            <PWAInstaller
              onInstallSuccess={() => console.log('PWA installed successfully')}
              onInstallError={(error) => console.error('PWA install failed:', error)}
            />
          </div>

          {/* Service Worker Management */}
          <ServiceWorkerManager
            onStateChange={handleServiceWorkerStateChange}
            onUpdateAvailable={() => console.log('Service Worker update available')}
            onUpdateInstalled={() => console.log('Service Worker update installed')}
          />
        </TabsContent>

        <TabsContent value="requirements" className="space-y-6">
          {/* Core Requirements */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Core PWA Requirements
              </CardTitle>
              <CardDescription>
                Essential requirements that must be met for a basic PWA
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {coreRequirements.map((req) => {
                  const Icon = req.icon
                  return (
                    <div key={req.id} className="flex items-start justify-between p-4 border rounded-lg">
                      <div className="flex items-start gap-3">
                        <Icon className={cn("h-5 w-5 mt-0.5", getStatusColor(req.status))} />
                        <div>
                          <h4 className="font-medium">{req.name}</h4>
                          <p className="text-sm text-muted-foreground">{req.description}</p>
                          <p className="text-xs text-muted-foreground mt-1">{req.details}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(req.status)}
                        {getStatusBadge(req.status)}
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="features" className="space-y-6">
          {/* Feature Requirements */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  PWA Features
                </CardTitle>
                <CardDescription>
                  Advanced PWA capabilities and features
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {featureRequirements.map((req) => {
                    const Icon = req.icon
                    return (
                      <div key={req.id} className="flex items-start justify-between p-3 border rounded-lg">
                        <div className="flex items-start gap-3">
                          <Icon className={cn("h-4 w-4 mt-0.5", getStatusColor(req.status))} />
                          <div>
                            <h5 className="font-medium text-sm">{req.name}</h5>
                            <p className="text-xs text-muted-foreground">{req.details}</p>
                          </div>
                        </div>
                        {getStatusIcon(req.status)}
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Enhancements
                </CardTitle>
                <CardDescription>
                  Additional enhancements for better user experience
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {enhancementRequirements.map((req) => {
                    const Icon = req.icon
                    return (
                      <div key={req.id} className="flex items-start justify-between p-3 border rounded-lg">
                        <div className="flex items-start gap-3">
                          <Icon className={cn("h-4 w-4 mt-0.5", getStatusColor(req.status))} />
                          <div>
                            <h5 className="font-medium text-sm">{req.name}</h5>
                            <p className="text-xs text-muted-foreground">{req.details}</p>
                          </div>
                        </div>
                        {getStatusIcon(req.status)}
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="testing" className="space-y-6">
          {/* PWA Testing Suite */}
          <div className="grid gap-6 md:grid-cols-2">
            <PWATest />
            <NotificationTest />
          </div>

          {/* Event Logger */}
          <PWAEventLogger
            onEventLogged={handleEventLogged}
            maxEvents={100}
            showFilters={true}
            showExport={true}
          />
        </TabsContent>

        <TabsContent value="manifest" className="space-y-6">
          {/* Manifest Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Web App Manifest
              </CardTitle>
              <CardDescription>
                Configuration details for the Progressive Web App
              </CardDescription>
            </CardHeader>
            <CardContent>
              {manifestData ? (
                <div className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <h4 className="font-medium mb-2">Basic Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Name:</span>
                          <span className="font-medium">{manifestData.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Short Name:</span>
                          <span className="font-medium">{manifestData.short_name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Display Mode:</span>
                          <span className="font-medium">{manifestData.display}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Start URL:</span>
                          <span className="font-medium">{manifestData.start_url}</span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Appearance</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Theme Color:</span>
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-4 h-4 rounded border"
                              style={{ backgroundColor: manifestData.theme_color }}
                            />
                            <span className="font-medium">{manifestData.theme_color}</span>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Background Color:</span>
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-4 h-4 rounded border"
                              style={{ backgroundColor: manifestData.background_color }}
                            />
                            <span className="font-medium">{manifestData.background_color}</span>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Orientation:</span>
                          <span className="font-medium">{manifestData.orientation}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {manifestData.icons && (
                    <div>
                      <h4 className="font-medium mb-2">Icons</h4>
                      <div className="grid gap-2 md:grid-cols-3">
                        {manifestData.icons.map((icon: any, index: number) => (
                          <div key={index} className="flex items-center gap-2 p-2 border rounded">
                            <img src={icon.src} alt="App Icon" className="w-8 h-8" />
                            <div className="text-xs">
                              <div className="font-medium">{icon.sizes}</div>
                              <div className="text-muted-foreground">{icon.type}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <BookOpen className="h-8 w-8 mx-auto mb-2" />
                  <p>Loading manifest data...</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="docs" className="space-y-6">
          {/* Documentation */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Smartphone className="h-5 w-5" />
                  Installation Instructions
                </CardTitle>
                <CardDescription>
                  How to install this PWA on different platforms
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Chrome/Edge (Desktop)</h4>
                    <ol className="list-decimal list-inside text-sm text-muted-foreground space-y-1">
                      <li>Look for the install icon in the address bar</li>
                      <li>Click the install button or use the "Install App" button</li>
                      <li>Confirm installation in the dialog</li>
                    </ol>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Safari (iOS)</h4>
                    <ol className="list-decimal list-inside text-sm text-muted-foreground space-y-1">
                      <li>Tap the Share button in Safari</li>
                      <li>Scroll down and tap "Add to Home Screen"</li>
                      <li>Tap "Add" to confirm</li>
                    </ol>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Chrome (Android)</h4>
                    <ol className="list-decimal list-inside text-sm text-muted-foreground space-y-1">
                      <li>Tap the menu button (three dots)</li>
                      <li>Select "Add to Home screen" or "Install app"</li>
                      <li>Confirm installation</li>
                    </ol>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Browser Compatibility
                </CardTitle>
                <CardDescription>
                  PWA feature support across different browsers
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Chrome/Chromium</span>
                    <Badge variant="default" className="bg-green-600">Full Support</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Firefox</span>
                    <Badge variant="default" className="bg-green-600">Good Support</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Safari</span>
                    <Badge variant="secondary" className="bg-yellow-600 text-white">Partial Support</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Edge</span>
                    <Badge variant="default" className="bg-green-600">Full Support</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Samsung Internet</span>
                    <Badge variant="default" className="bg-green-600">Good Support</Badge>
                  </div>
                </div>
                
                <Alert className="mt-4">
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Some features like push notifications and background sync may have limited support on iOS Safari.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>

          {/* Troubleshooting */}
          <Card>
            <CardHeader>
              <CardTitle>Troubleshooting</CardTitle>
              <CardDescription>
                Common issues and solutions for PWA functionality
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Install button not showing</h4>
                  <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1">
                    <li>Ensure you're using HTTPS or localhost</li>
                    <li>Check that the manifest.json is valid and accessible</li>
                    <li>Verify service worker is registered successfully</li>
                    <li>Try refreshing the page or clearing browser cache</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Offline functionality not working</h4>
                  <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1">
                    <li>Check service worker registration status</li>
                    <li>Verify caching strategies are implemented</li>
                    <li>Test with browser developer tools offline mode</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Notifications not working</h4>
                  <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1">
                    <li>Check notification permissions in browser settings</li>
                    <li>Ensure HTTPS is being used</li>
                    <li>Verify push notification setup is correct</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AppLayout>
  );
}
