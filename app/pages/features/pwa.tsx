import type { Route } from "./+types/pwa";
import { useState, useCallback } from "react"
import { AppLayout } from "~/components/layouts/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import {
  PWAInstaller,
  ServiceWorkerManager,
  PWAStatus, 
  PWAEventLogger,
  PWATest
} from "~/components/base/pwa"
import type { ServiceWorkerStatus, PWAEvent } from "~/components/base/pwa"

export function meta({}: Route.MetaArgs) {
  return [
    { title: "PWA Features - Admin Panel" },
    { name: "description", content: "Progressive Web App features and status monitoring" },
  ];
}

export default function PWAPage() {
  const [serviceWorkerStatus, setServiceWorkerStatus] = useState<ServiceWorkerStatus | undefined>()
  const [eventCount, setEventCount] = useState(0)

  const breadcrumbItems = [
    { title: "Home", href: "/" },
    { title: "PWA Features", isCurrentPage: true }
  ];

  const handleServiceWorkerStateChange = useCallback((status: ServiceWorkerStatus) => {
    setServiceWorkerStatus(status)
  }, [])

  const handleEventLogged = useCallback((event: PWAEvent) => {
    setEventCount(prev => prev + 1)
  }, [])

  return (
    <AppLayout
      title="PWA Features"
      breadcrumbItems={breadcrumbItems}
      className="gap-6 p-6 pt-0"
    >
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Progressive Web App</h1>
        <p className="text-muted-foreground">
          Monitor and manage PWA features including installation, service workers, and offline capabilities.
        </p>
      </div>

      {/* PWA Status Overview */}
      <div className="grid gap-6 md:grid-cols-2">
        <PWAStatus
          serviceWorkerStatus={serviceWorkerStatus}
        />
        <PWAInstaller
          onInstallSuccess={() => console.log('PWA installed successfully')}
          onInstallError={(error) => console.error('PWA install failed:', error)}
        />
      </div>

      {/* Service Worker Management */}
      <ServiceWorkerManager
        onStateChange={handleServiceWorkerStateChange}
        onUpdateAvailable={() => console.log('Service Worker update available')}
        onUpdateInstalled={() => console.log('Service Worker update installed')}
      />

      {/* PWA Compatibility Test */}
      <PWATest />

      {/* Installation Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Installation Instructions</CardTitle>
          <CardDescription>
            How to install this app on different devices and browsers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="space-y-2">
              <h4 className="font-medium">Chrome/Edge (Desktop)</h4>
              <p className="text-sm text-muted-foreground">
                Look for the install icon in the address bar or use the "Install App" button above.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Safari (iOS)</h4>
              <p className="text-sm text-muted-foreground">
                Tap the share button and select "Add to Home Screen".
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Chrome (Android)</h4>
              <p className="text-sm text-muted-foreground">
                Tap the menu button and select "Add to Home screen" or "Install app".
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Service Worker Benefits */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Service Worker Benefits</CardTitle>
            <CardDescription>
              Features enabled by Service Worker technology
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li>• Offline functionality</li>
              <li>• Background sync</li>
              <li>• Push notifications</li>
              <li>• Caching strategies</li>
              <li>• Performance improvements</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Current Status</CardTitle>
            <CardDescription>
              Real-time Service Worker information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>State:</span>
                <span className="font-medium">{serviceWorkerStatus?.state || 'Unknown'}</span>
              </div>
              <div className="flex justify-between">
                <span>Registration:</span>
                <span className="font-medium">
                  {serviceWorkerStatus?.registration ? 'Active' : 'None'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Update Available:</span>
                <span className="font-medium">
                  {serviceWorkerStatus?.updateAvailable ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Event Logger */}
      <PWAEventLogger
        maxEvents={50}
        autoScroll={true}
        showTimestamps={true}
        onEventLogged={handleEventLogged}
      />
    </AppLayout>
  )
}
