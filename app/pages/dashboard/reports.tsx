import type { Route } from "./+types/reports";
import { AppLayout } from "~/components/layouts/app-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { 
  FileText, 
  Download, 
  Calendar, 
  Filter, 
  BarChart3, 
  PieChart, 
  TrendingUp,
  FileBarChart,
  FileSpreadsheet,
  FilePieChart
} from "lucide-react";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Reports - Admin Panel" },
    { name: "description", content: "Generate and manage reports" },
  ];
}

export default function ReportsPage() {
  const breadcrumbItems = [
    { title: "Home", href: "/" },
    { title: "Reports", isCurrentPage: true }
  ];

  const reportTemplates = [
    {
      id: 1,
      name: "User Activity Report",
      description: "Detailed analysis of user engagement and activity patterns",
      type: "Analytics",
      icon: BarChart3,
      frequency: "Weekly",
      lastGenerated: "2 days ago",
      status: "ready"
    },
    {
      id: 2,
      name: "Financial Summary",
      description: "Revenue, expenses, and financial performance metrics",
      type: "Financial",
      icon: PieChart,
      frequency: "Monthly",
      lastGenerated: "1 week ago",
      status: "ready"
    },
    {
      id: 3,
      name: "System Performance",
      description: "Server performance, uptime, and technical metrics",
      type: "Technical",
      icon: TrendingUp,
      frequency: "Daily",
      lastGenerated: "6 hours ago",
      status: "generating"
    },
    {
      id: 4,
      name: "Security Audit",
      description: "Security events, login attempts, and access logs",
      type: "Security",
      icon: FileBarChart,
      frequency: "Monthly",
      lastGenerated: "3 days ago",
      status: "ready"
    }
  ];

  const recentReports = [
    {
      id: 1,
      name: "Q4 2024 Analytics Report",
      type: "PDF",
      size: "2.4 MB",
      generatedBy: "John Doe",
      generatedAt: "2024-01-15 14:30",
      downloads: 23,
      icon: FileText
    },
    {
      id: 2,
      name: "User Engagement Data",
      type: "Excel",
      size: "1.8 MB",
      generatedBy: "Jane Smith",
      generatedAt: "2024-01-14 09:15",
      downloads: 45,
      icon: FileSpreadsheet
    },
    {
      id: 3,
      name: "Revenue Analysis Chart",
      type: "PDF",
      size: "3.2 MB",
      generatedBy: "Mike Johnson",
      generatedAt: "2024-01-13 16:45",
      downloads: 12,
      icon: FilePieChart
    },
    {
      id: 4,
      name: "System Health Report",
      type: "PDF",
      size: "1.1 MB",
      generatedBy: "Sarah Wilson",
      generatedAt: "2024-01-12 11:20",
      downloads: 8,
      icon: FileBarChart
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ready":
        return <Badge className="bg-green-500">Ready</Badge>;
      case "generating":
        return <Badge className="bg-blue-500">Generating</Badge>;
      case "error":
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    const colors: Record<string, string> = {
      "Analytics": "bg-blue-500",
      "Financial": "bg-green-500",
      "Technical": "bg-purple-500",
      "Security": "bg-red-500"
    };
    return <Badge className={colors[type] || "bg-gray-500"}>{type}</Badge>;
  };

  return (
    <AppLayout
      title="Reports"
      breadcrumbItems={breadcrumbItems}
      className="gap-6 p-6 pt-0"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
          <p className="text-muted-foreground">
            Generate, schedule, and manage your business reports.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            Schedule
          </Button>
          <Button>
            <FileText className="mr-2 h-4 w-4" />
            New Report
          </Button>
        </div>
      </div>

      {/* Report Templates */}
      <Card>
        <CardHeader>
          <CardTitle>Report Templates</CardTitle>
          <CardDescription>
            Pre-configured report templates for common business needs.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            {reportTemplates.map((template) => {
              const Icon = template.icon;
              return (
                <div key={template.id} className="p-4 border rounded-lg space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <Icon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-medium">{template.name}</h3>
                        <p className="text-sm text-muted-foreground">{template.description}</p>
                      </div>
                    </div>
                    {getStatusBadge(template.status)}
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-4">
                      {getTypeBadge(template.type)}
                      <span className="text-muted-foreground">
                        {template.frequency} • Last: {template.lastGenerated}
                      </span>
                    </div>
                    <Button size="sm" disabled={template.status === "generating"}>
                      {template.status === "generating" ? "Generating..." : "Generate"}
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recent Reports */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Reports</CardTitle>
          <CardDescription>
            Recently generated reports available for download.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentReports.map((report) => {
              const Icon = report.icon;
              return (
                <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <Icon className="h-8 w-8 text-muted-foreground" />
                    <div>
                      <h4 className="font-medium">{report.name}</h4>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>{report.type} • {report.size}</span>
                        <span>Generated by {report.generatedBy}</span>
                        <span>{report.generatedAt}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right text-sm">
                      <div className="font-medium">{report.downloads} downloads</div>
                    </div>
                    <Button size="sm" variant="outline">
                      <Download className="mr-2 h-4 w-4" />
                      Download
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reports Generated</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">127</div>
            <p className="text-xs text-muted-foreground">
              +12 from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Downloads</CardTitle>
            <Download className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,234</div>
            <p className="text-xs text-muted-foreground">
              +23% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduled Reports</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">
              Active schedules
            </p>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
