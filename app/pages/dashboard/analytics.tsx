import type { Route } from "./+types/analytics";
import { AppLayout } from "~/components/layouts/app-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Eye, 
  MousePointer, 
  Clock,
  Globe,
  Smartphone,
  Monitor
} from "lucide-react";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Analytics - Admin Panel" },
    { name: "description", content: "Analytics and performance metrics" },
  ];
}

export default function AnalyticsPage() {
  const breadcrumbItems = [
    { title: "Home", href: "/" },
    { title: "Analytics", isCurrentPage: true }
  ];

  const metrics = [
    {
      title: "Total Visitors",
      value: "45,231",
      change: "+12.5%",
      trend: "up",
      icon: Users,
      description: "Unique visitors this month"
    },
    {
      title: "Page Views",
      value: "123,456",
      change: "****%",
      trend: "up",
      icon: Eye,
      description: "Total page views this month"
    },
    {
      title: "Bounce Rate",
      value: "34.2%",
      change: "-2.1%",
      trend: "down",
      icon: MousePointer,
      description: "Percentage of single-page visits"
    },
    {
      title: "Avg. Session",
      value: "4m 32s",
      change: "+15.3%",
      trend: "up",
      icon: Clock,
      description: "Average session duration"
    }
  ];

  const topPages = [
    { page: "/dashboard", views: 12543, percentage: 28.5 },
    { page: "/users", views: 8932, percentage: 20.3 },
    { page: "/analytics", views: 6721, percentage: 15.3 },
    { page: "/settings", views: 4567, percentage: 10.4 },
    { page: "/reports", views: 3421, percentage: 7.8 }
  ];

  const deviceStats = [
    { device: "Desktop", users: 18543, percentage: 65.2, icon: Monitor },
    { device: "Mobile", users: 8932, percentage: 31.4, icon: Smartphone },
    { device: "Tablet", users: 967, percentage: 3.4, icon: Globe }
  ];

  const recentActivity = [
    { time: "2 min ago", event: "New user registration", user: "<EMAIL>" },
    { time: "5 min ago", event: "Page view", user: "Dashboard <NAME_EMAIL>" },
    { time: "8 min ago", event: "User login", user: "<EMAIL>" },
    { time: "12 min ago", event: "Settings updated", user: "<EMAIL>" },
    { time: "15 min ago", event: "Report generated", user: "<EMAIL>" }
  ];

  return (
    <AppLayout
      title="Analytics"
      breadcrumbItems={breadcrumbItems}
      className="gap-6 p-6 pt-0"
    >
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
        <p className="text-muted-foreground">
          Track your application performance and user engagement metrics.
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric) => {
          const Icon = metric.icon;
          const isPositive = metric.trend === "up";
          const TrendIcon = isPositive ? TrendingUp : TrendingDown;
          
          return (
            <Card key={metric.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metric.value}</div>
                <div className="flex items-center gap-1 text-xs">
                  <TrendIcon className={`h-3 w-3 ${isPositive ? 'text-green-600' : 'text-red-600'}`} />
                  <span className={isPositive ? 'text-green-600' : 'text-red-600'}>
                    {metric.change}
                  </span>
                  <span className="text-muted-foreground">from last month</span>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {metric.description}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Charts and Data */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Top Pages */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Top Pages
            </CardTitle>
            <CardDescription>
              Most visited pages this month
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topPages.map((page, index) => (
                <div key={page.page} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground text-xs flex items-center justify-center">
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium">{page.page}</div>
                      <div className="text-sm text-muted-foreground">{page.views.toLocaleString()} views</div>
                    </div>
                  </div>
                  <Badge variant="outline">{page.percentage}%</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Device Statistics */}
        <Card>
          <CardHeader>
            <CardTitle>Device Breakdown</CardTitle>
            <CardDescription>
              User distribution by device type
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {deviceStats.map((device) => {
                const Icon = device.icon;
                return (
                  <div key={device.device} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Icon className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <div className="font-medium">{device.device}</div>
                        <div className="text-sm text-muted-foreground">
                          {device.users.toLocaleString()} users
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{device.percentage}%</div>
                      <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-primary rounded-full"
                          style={{ width: `${device.percentage}%` }}
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>
            Latest user interactions and system events
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-center gap-4 p-3 border rounded-lg">
                <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0" />
                <div className="flex-1">
                  <div className="font-medium">{activity.event}</div>
                  <div className="text-sm text-muted-foreground">{activity.user}</div>
                </div>
                <div className="text-sm text-muted-foreground">{activity.time}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </AppLayout>
  );
}
