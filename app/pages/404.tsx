import type { Route } from "./+types/404";
import { AppLayout } from "~/components/layouts/app-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { 
  Home, 
  Search, 
  ArrowLeft, 
  FileQuestion,
  BarChart3,
  Users,
  Settings,
  Smartphone
} from "lucide-react";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Page Not Found - Admin Panel" },
    { name: "description", content: "The requested page could not be found" },
  ];
}

export default function NotFoundPage() {
  const breadcrumbItems = [
    { title: "Home", href: "/" },
    { title: "404 - Page Not Found", isCurrentPage: true }
  ];

  const quickLinks = [
    {
      title: "Dashboard",
      description: "View your main dashboard",
      href: "/dashboard",
      icon: BarChart3,
      color: "bg-blue-500"
    },
    {
      title: "Users",
      description: "Manage user accounts",
      href: "/users",
      icon: Users,
      color: "bg-green-500"
    },
    {
      title: "Settings",
      description: "Configure your application",
      href: "/settings",
      icon: Settings,
      color: "bg-purple-500"
    },
    {
      title: "PWA Features",
      description: "Progressive Web App tools",
      href: "/pwa",
      icon: Smartphone,
      color: "bg-orange-500"
    }
  ];

  const handleGoBack = () => {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      window.location.href = '/';
    }
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  const handleSearch = () => {
    // In a real app, this would open a search modal or navigate to search page
    console.log('Search functionality would be implemented here');
  };

  return (
    <AppLayout
      title="Page Not Found"
      breadcrumbItems={breadcrumbItems}
      className="gap-6 p-6 pt-0"
    >
      {/* Main 404 Content */}
      <div className="flex flex-col items-center justify-center min-h-[400px] text-center space-y-6">
        <div className="relative">
          <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center">
            <FileQuestion className="w-12 h-12 text-muted-foreground" />
          </div>
          <Badge className="absolute -top-2 -right-2 bg-red-500">404</Badge>
        </div>
        
        <div className="space-y-2">
          <h1 className="text-4xl font-bold tracking-tight">Page Not Found</h1>
          <p className="text-xl text-muted-foreground max-w-md">
            Sorry, we couldn't find the page you're looking for.
          </p>
          <p className="text-sm text-muted-foreground">
            The page may have been moved, deleted, or you may have entered an incorrect URL.
          </p>
        </div>

        <div className="flex gap-3">
          <Button onClick={handleGoBack} variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Go Back
          </Button>
          <Button onClick={handleGoHome}>
            <Home className="mr-2 h-4 w-4" />
            Go Home
          </Button>
          <Button onClick={handleSearch} variant="outline">
            <Search className="mr-2 h-4 w-4" />
            Search
          </Button>
        </div>
      </div>

      {/* Quick Links */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Links</CardTitle>
          <CardDescription>
            Here are some popular pages you might be looking for:
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {quickLinks.map((link) => {
              const Icon = link.icon;
              return (
                <a
                  key={link.href}
                  href={link.href}
                  className="group block p-4 border rounded-lg hover:border-primary transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${link.color} text-white group-hover:scale-110 transition-transform`}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div>
                      <h3 className="font-medium group-hover:text-primary transition-colors">
                        {link.title}
                      </h3>
                      <p className="text-xs text-muted-foreground">
                        {link.description}
                      </p>
                    </div>
                  </div>
                </a>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle>Need Help?</CardTitle>
          <CardDescription>
            If you're having trouble finding what you're looking for, here are some options:
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <h4 className="font-medium">Check the URL</h4>
              <p className="text-sm text-muted-foreground">
                Make sure the web address is spelled correctly and try again.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Use Navigation</h4>
              <p className="text-sm text-muted-foreground">
                Try using the sidebar navigation to find the page you're looking for.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Contact Support</h4>
              <p className="text-sm text-muted-foreground">
                If you believe this is an error, please contact our support team.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </AppLayout>
  );
}
