// Mock data for dashboard
export const dashboardStats = {
  totalRevenue: {
    value: 45231.89,
    change: 20.1,
    period: "from last month"
  },
  subscriptions: {
    value: 2350,
    change: 180.1,
    period: "from last month"
  },
  sales: {
    value: 12234,
    change: 19,
    period: "from last month"
  },
  activeNow: {
    value: 573,
    change: 201,
    period: "from last hour"
  }
}

export const recentSales = [
  {
    id: "1",
    customer: "<PERSON>",
    email: "<EMAIL>",
    amount: 1999.00,
    avatar: "/avatars/01.png"
  },
  {
    id: "2", 
    customer: "<PERSON>",
    email: "<EMAIL>",
    amount: 39.00,
    avatar: "/avatars/02.png"
  },
  {
    id: "3",
    customer: "<PERSON>", 
    email: "<EMAIL>",
    amount: 299.00,
    avatar: "/avatars/03.png"
  },
  {
    id: "4",
    customer: "<PERSON>",
    email: "<EMAIL>", 
    amount: 99.00,
    avatar: "/avatars/04.png"
  },
  {
    id: "5",
    customer: "<PERSON>",
    email: "<EMAIL>",
    amount: 39.00,
    avatar: "/avatars/05.png"
  }
]

export const chartData = [
  { name: "Jan", total: Math.floor(Math.random() * 5000) + 1000 },
  { name: "Feb", total: Math.floor(Math.random() * 5000) + 1000 },
  { name: "Mar", total: Math.floor(Math.random() * 5000) + 1000 },
  { name: "Apr", total: Math.floor(Math.random() * 5000) + 1000 },
  { name: "May", total: Math.floor(Math.random() * 5000) + 1000 },
  { name: "Jun", total: Math.floor(Math.random() * 5000) + 1000 },
  { name: "Jul", total: Math.floor(Math.random() * 5000) + 1000 },
  { name: "Aug", total: Math.floor(Math.random() * 5000) + 1000 },
  { name: "Sep", total: Math.floor(Math.random() * 5000) + 1000 },
  { name: "Oct", total: Math.floor(Math.random() * 5000) + 1000 },
  { name: "Nov", total: Math.floor(Math.random() * 5000) + 1000 },
  { name: "Dec", total: Math.floor(Math.random() * 5000) + 1000 },
]

export const recentOrders = [
  {
    id: "ORD-001",
    customer: "Liam Johnson",
    email: "<EMAIL>",
    status: "Fulfilled",
    date: "2024-01-15",
    amount: 250.00
  },
  {
    id: "ORD-002", 
    customer: "Olivia Smith",
    email: "<EMAIL>",
    status: "Pending",
    date: "2024-01-14",
    amount: 150.00
  },
  {
    id: "ORD-003",
    customer: "Noah Williams",
    email: "<EMAIL>", 
    status: "Fulfilled",
    date: "2024-01-13",
    amount: 350.00
  },
  {
    id: "ORD-004",
    customer: "Emma Brown",
    email: "<EMAIL>",
    status: "Fulfilled", 
    date: "2024-01-12",
    amount: 450.00
  },
  {
    id: "ORD-005",
    customer: "Oliver Jones",
    email: "<EMAIL>",
    status: "Cancelled",
    date: "2024-01-11", 
    amount: 550.00
  }
]

export const topProducts = [
  {
    id: "1",
    name: "Wireless Headphones",
    sales: 1234,
    revenue: 98720.00,
    growth: 12.5
  },
  {
    id: "2", 
    name: "Smart Watch",
    sales: 987,
    revenue: 78960.00,
    growth: 8.2
  },
  {
    id: "3",
    name: "Laptop Stand",
    sales: 756,
    revenue: 45360.00,
    growth: -2.1
  },
  {
    id: "4",
    name: "USB-C Cable",
    sales: 543,
    revenue: 21720.00,
    growth: 15.7
  },
  {
    id: "5",
    name: "Bluetooth Speaker",
    sales: 432,
    revenue: 34560.00,
    growth: 5.3
  }
]
