import React from "react"
import { AppSidebar } from "~/components/base/app-sidebar"
import { DashboardHeader } from "~/components/base/header"
import { SidebarInset, SidebarProvider } from "~/components/ui/sidebar"
import { HeaderActions } from "~/components/base/header-actions"
import { useHeaderActions } from "~/hooks/use-header-actions"
import type { UseHeaderActionsOptions } from "~/hooks/use-header-actions"
import { HeaderFixTopDiv } from '../base/header-fix-top-div';

export interface BreadcrumbItem {
  title: string
  href?: string
  isCurrentPage?: boolean
}

export interface AppLayoutProps {
  title: string
  breadcrumbItems?: BreadcrumbItem[]
  children: React.ReactNode
  headerOptions?: UseHeaderActionsOptions
  customActions?: React.ReactNode
  className?: string
}

export function AppLayout({
  title,
  breadcrumbItems = [],
  children,
  headerOptions = {},
  customActions,
  className
}: AppLayoutProps) {
  const { actions, isLoading, error } = useHeaderActions({
    callbacks: {
      onSearch: () => console.log('Search clicked'),
      onNotificationsView: () => console.log('View all notifications'),
      onMessagesView: () => console.log('View all messages'),
      onSettings: () => console.log('Settings clicked'),
      onProfile: () => console.log('Profile clicked'),
    },
    ...headerOptions
  })

  // Use custom actions if provided, otherwise use the hook-generated actions
  const rightActions = customActions || <HeaderActions actions={actions} />

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <HeaderFixTopDiv>
          <DashboardHeader
            title={title}
            breadcrumbItems={breadcrumbItems}
            rightActions={rightActions}
            />
        </HeaderFixTopDiv>
        <div className={`flex flex-1 flex-col gap-4 p-4 pt-0 overflow-auto ${className || ''}`}>
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              Error loading header data: {error}
            </div>
          )}
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}

// Convenience wrapper for pages that need custom header actions
export interface AppLayoutWithCustomActionsProps extends Omit<AppLayoutProps, 'customActions'> {
  renderCustomActions?: (defaultActions: React.ReactNode) => React.ReactNode
}

export function AppLayoutWithCustomActions({
  renderCustomActions,
  ...props
}: AppLayoutWithCustomActionsProps) {
  const { actions } = useHeaderActions(props.headerOptions)
  const defaultActions = <HeaderActions actions={actions} />
  
  return (
    <AppLayout
      {...props}
      customActions={renderCustomActions ? renderCustomActions(defaultActions) : defaultActions}
    />
  )
}
