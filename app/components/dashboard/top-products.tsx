import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { But<PERSON> } from "~/components/ui/button"
import {
  ArrowDownRight,
  TrendingUp,
  Package,
} from "lucide-react"
import { topProducts } from "~/lib/mock-data"

export function TopProducts() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center">
        <div className="grid gap-2">
          <CardTitle>Top Products</CardTitle>
          <CardDescription>
            Your best performing products this month.
          </CardDescription>
        </div>
        <Button asChild size="sm" className="ml-auto gap-1">
          <a href="#">
            View All
            <Package className="h-4 w-4" />
          </a>
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {topProducts.map((product) => (
            <div key={product.id} className="flex items-center">
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">
                  {product.name}
                </p>
                <p className="text-sm text-muted-foreground">
                  {product.sales} sales
                </p>
              </div>
              <div className="ml-auto space-y-1 text-right">
                <p className="text-sm font-medium">
                  ${product.revenue.toLocaleString()}
                </p>
                <p className={`text-xs flex items-center justify-end ${
                  product.growth > 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {product.growth > 0 ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-3 w-3 mr-1" />
                  )}
                  {Math.abs(product.growth)}%
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
