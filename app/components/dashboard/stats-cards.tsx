import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "~/components/ui/card"
import { 
  Activity, 
  CreditCard, 
  DollarSign, 
  Users,
  ArrowUpRight,
} from "lucide-react"
import { dashboardStats } from "~/lib/mock-data"

export function StatsCards() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Total Revenue
          </CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            ${dashboardStats.totalRevenue.value.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground flex items-center">
            <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
            +{dashboardStats.totalRevenue.change}% {dashboardStats.totalRevenue.period}
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Subscriptions
          </CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            +{dashboardStats.subscriptions.value.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground flex items-center">
            <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
            +{dashboardStats.subscriptions.change}% {dashboardStats.subscriptions.period}
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Sales</CardTitle>
          <CreditCard className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            +{dashboardStats.sales.value.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground flex items-center">
            <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
            +{dashboardStats.sales.change}% {dashboardStats.sales.period}
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active Now</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            +{dashboardStats.activeNow.value}
          </div>
          <p className="text-xs text-muted-foreground flex items-center">
            <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
            +{dashboardStats.activeNow.change} {dashboardStats.activeNow.period}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
