import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "~/components/ui/card"
import { BarChart3 } from "lucide-react"

export function OverviewChart() {
  return (
    <Card className="col-span-4">
      <CardHeader>
        <CardTitle>Overview</CardTitle>
      </CardHeader>
      <CardContent className="pl-2">
        <div className="h-[200px] flex items-center justify-center bg-muted/50 rounded-lg">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Chart visualization would go here</p>
            <p className="text-xs text-muted-foreground">Revenue over time</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
