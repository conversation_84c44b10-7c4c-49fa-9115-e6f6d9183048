import React from "react"
import type { DropdownContentData } from "~/services/header-notifications"

export interface DropdownContentProps {
  data: DropdownContentData
  className?: string
}

export function DropdownContent({ data, className }: DropdownContentProps) {
  return (
    <div className={className}>
      {/* Header */}
      <div className="px-3 py-2 border-b border-border">
        <div className="font-semibold">{data.header.title}</div>
        <div className="text-xs text-muted-foreground">
          {data.header.subtitle}
        </div>
      </div>
      
      {/* Items */}
      <div className="p-1">
        {data.items.map((item) => (
          <button
            key={item.id}
            type="button"
            onClick={item.onClick}
            className="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none transition-colors duration-200"
          >
            <div className="flex flex-col gap-1">
              <div className="font-medium">{item.title}</div>
              <div className="text-xs text-muted-foreground">{item.subtitle}</div>
            </div>
          </button>
        ))}
        
        {/* Footer */}
        {data.footer && (
          <>
            <div className="h-px bg-border my-1" />
            <button
              type="button"
              onClick={data.footer.onClick}
              className="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none transition-colors duration-200"
            >
              <div className="text-center text-sm text-primary">{data.footer.text}</div>
            </button>
          </>
        )}
      </div>
    </div>
  )
}
