import type { LucideIcon } from "lucide-react"
import { Bell, MessageSquare, Mail, AlertCircle } from "lucide-react"
import { HeaderIcon } from "./icons/header-icons"
import type { HeaderIconProps } from "./icons/header-icons"
import { HeaderIconDropdown } from "./icons/header-icons-dropdown"
import type { HeaderIconDropdownProps } from "./icons/header-icons-dropdown"
import { NotificationBadge } from "./badge"
import type { BadgeColor, BadgeShape } from "./badge"

export interface IconMessageProps extends Omit<HeaderIconProps, "onClick"> {
  icon: LucideIcon
  messageCount?: number
  messageText?: string
  badgeColor?: BadgeColor
  badgeShape?: BadgeShape
  blinkInterval?: number
  showBadge?: boolean
  onClick?: () => void
  className?: string
}

export function IconMessage({
  icon,
  size = 20,
  messageCount,
  messageText,
  badgeColor = "red",
  badgeShape = "circular",
  blinkInterval = 0,
  showBadge = true,
  onClick,
  className,
  ...props
}: IconMessageProps) {
  // Determine badge text - use messageText if provided, otherwise messageCount
  const badgeText = messageText || (messageCount !== undefined ? messageCount.toString() : "")
  
  // Only show badge if there's content and showBadge is true
  const shouldShowBadge = showBadge && badgeText && (messageCount === undefined || messageCount > 0)

  return (
    <div className="relative">
      <HeaderIcon
        icon={icon}
        size={size}
        onClick={onClick}
        className={className}
        {...props}
      />
      
      {shouldShowBadge && (
        <div className="absolute -top-1 -right-1">
          <NotificationBadge
            text={badgeText}
            color={badgeColor}
            shape={badgeShape}
            blinkInterval={blinkInterval}
          />
        </div>
      )}
    </div>
  )
}

// Icon message with dropdown functionality
export interface IconMessageDropdownProps extends Omit<HeaderIconDropdownProps, "icon"> {
  icon: LucideIcon
  messageCount?: number
  messageText?: string
  badgeColor?: BadgeColor
  badgeShape?: BadgeShape
  blinkInterval?: number
  showBadge?: boolean
}

export function IconMessageDropdown({
  icon,
  size = 20,
  messageCount,
  messageText,
  badgeColor = "red",
  badgeShape = "circular", 
  blinkInterval = 0,
  showBadge = true,
  dropdownContent,
  position = "right",
  onOpenChange,
  className,
  ...props
}: IconMessageDropdownProps) {
  // Determine badge text
  const badgeText = messageText || (messageCount !== undefined ? messageCount.toString() : "")
  
  // Only show badge if there's content and showBadge is true
  const shouldShowBadge = showBadge && badgeText && (messageCount === undefined || messageCount > 0)

  return (
    <div className="relative">
      <HeaderIconDropdown
        icon={icon}
        size={size}
        dropdownContent={dropdownContent}
        position={position}
        onOpenChange={onOpenChange}
        className={className}
        {...props}
      />
      
      {shouldShowBadge && (
        <div className="absolute -top-1 -right-1 pointer-events-none">
          <NotificationBadge
            text={badgeText}
            color={badgeColor}
            shape={badgeShape}
            blinkInterval={blinkInterval}
          />
        </div>
      )}
    </div>
  )
}

// Preset components for common use cases
export interface NotificationIconProps extends Omit<IconMessageProps, "icon"> {
  type?: "bell" | "message" | "mail" | "alert"
}

export function NotificationIcon({ type = "bell", ...props }: NotificationIconProps) {
  const iconMap = {
    bell: Bell,
    message: MessageSquare,
    mail: Mail,
    alert: AlertCircle
  }

  const Icon = iconMap[type]

  return <IconMessage icon={Icon} {...props} />
}

export interface NotificationIconDropdownProps extends Omit<IconMessageDropdownProps, "icon"> {
  type?: "bell" | "message" | "mail" | "alert"
}

export function NotificationIconDropdown({ type = "bell", ...props }: NotificationIconDropdownProps) {
  const iconMap = {
    bell: Bell,
    message: MessageSquare,
    mail: Mail,
    alert: AlertCircle
  }

  const Icon = iconMap[type]

  return <IconMessageDropdown icon={Icon} {...props} />
}
