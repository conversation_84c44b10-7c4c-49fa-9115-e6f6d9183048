import type { Route } from "./+types/pwa-analyzer-page";
import { useState } from "react"
import { AppLayout } from "~/components/layouts/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { Badge } from "~/components/ui/badge"
import { Alert, AlertDescription } from "~/components/ui/alert"
import { 
  BarChart2, 
  CheckCircle, 
  AlertCircle, 
  Info,
  Smartphone,
  Settings,
  Globe,
  Shield
} from "lucide-react"
import { generateBreadcrumbs } from "~/config/navigation"
import PwaAnalyzer from './pwa-analyzer'

export function meta({}: Route.MetaArgs) {
  return [
    { title: "PWA Analyzer - Admin Panel" },
    { name: "description", content: "Comprehensive Progressive Web App analysis and diagnostics tool" },
  ];
}

export default function PWAAnalyzerPage() {
  const [isAnalyzerOpen, setIsAnalyzerOpen] = useState(false)

  const breadcrumbItems = generateBreadcrumbs("/pwa-analyzer");

  const features = [
    {
      icon: CheckCircle,
      title: "Requirements Check",
      description: "Validates all PWA requirements including HTTPS, Service Worker, and Web App Manifest",
      color: "text-green-600"
    },
    {
      icon: Globe,
      title: "Manifest Analysis",
      description: "Detailed analysis of your Web App Manifest with validation and recommendations",
      color: "text-blue-600"
    },
    {
      icon: Settings,
      title: "Service Worker Status",
      description: "Comprehensive service worker registration and functionality testing",
      color: "text-purple-600"
    },
    {
      icon: Shield,
      title: "Security Validation",
      description: "Ensures your PWA meets security requirements and best practices",
      color: "text-orange-600"
    }
  ]

  const quickStats = [
    { label: "PWA Requirements", value: "6", description: "Core requirements checked" },
    { label: "Manifest Fields", value: "12+", description: "Manifest properties validated" },
    { label: "Icon Formats", value: "3", description: "Icon sizes and formats verified" },
    { label: "Browser Support", value: "95%", description: "Modern browser compatibility" }
  ]

  return (
    <AppLayout
      title="PWA Analyzer"
      breadcrumbItems={breadcrumbItems}
      className="gap-6 p-6 pt-0"
    >
      {/* Header Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">PWA Analyzer</h1>
            <p className="text-muted-foreground">
              Comprehensive analysis and diagnostics for your Progressive Web App
            </p>
          </div>
          <Button 
            onClick={() => setIsAnalyzerOpen(true)}
            className="flex items-center gap-2"
            size="lg"
          >
            <BarChart2 className="h-5 w-5" />
            Run Analysis
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          {quickStats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-primary">{stat.value}</div>
                <div className="font-medium">{stat.label}</div>
                <div className="text-xs text-muted-foreground">{stat.description}</div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Features Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5" />
            Analysis Features
          </CardTitle>
          <CardDescription>
            Comprehensive PWA analysis covering all essential requirements and best practices
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <div key={index} className="flex items-start gap-4 p-4 border rounded-lg">
                  <Icon className={`h-6 w-6 mt-1 ${feature.color}`} />
                  <div>
                    <h3 className="font-semibold mb-1">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground">{feature.description}</p>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Analysis Information */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>What Gets Analyzed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Badge variant="outline">HTTPS</Badge>
                <span className="text-sm">Secure connection validation</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">Service Worker</Badge>
                <span className="text-sm">Registration and functionality</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">Manifest</Badge>
                <span className="text-sm">Web App Manifest validation</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">Icons</Badge>
                <span className="text-sm">App icons and sizes</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">Installability</Badge>
                <span className="text-sm">Installation capability check</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">Theme</Badge>
                <span className="text-sm">Theme color and appearance</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Analysis Benefits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <strong>Comprehensive Validation:</strong> Checks all PWA requirements and provides detailed feedback on compliance.
                </AlertDescription>
              </Alert>
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Actionable Recommendations:</strong> Get specific guidance on how to fix any issues found during analysis.
                </AlertDescription>
              </Alert>
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Real-time Results:</strong> Instant analysis with live status updates and progress tracking.
                </AlertDescription>
              </Alert>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Call to Action */}
      <Card className="border-primary/20 bg-primary/5">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold mb-2">Ready to Analyze Your PWA?</h3>
              <p className="text-muted-foreground">
                Run a comprehensive analysis to ensure your Progressive Web App meets all requirements and best practices.
              </p>
            </div>
            <Button 
              onClick={() => setIsAnalyzerOpen(true)}
              size="lg"
              className="flex items-center gap-2"
            >
              <BarChart2 className="h-5 w-5" />
              Start Analysis
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* PWA Analyzer Dialog */}
      <PwaAnalyzer 
        open={isAnalyzerOpen} 
        onOpenChange={setIsAnalyzerOpen} 
      />
    </AppLayout>
  );
}
