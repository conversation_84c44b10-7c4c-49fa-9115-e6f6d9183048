import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { Badge } from "~/components/ui/badge"
import { Separator } from "~/components/ui/separator"
import {
  Wifi,
  WifiOff,
  Smartphone,
  Monitor,
  Globe,
  Settings,
  CheckCircle,
  AlertCircle,
  Clock
} from "lucide-react"
import { cn } from "~/lib/utils"
import type { ServiceWorkerStatus } from "./service-worker-manager"

export interface PWAStatusProps {
  className?: string
  serviceWorkerStatus?: ServiceWorkerStatus
}

export interface ConnectionStatus {
  online: boolean
  effectiveType?: string
  downlink?: number
  rtt?: number
}

export interface InstallationStatus {
  isInstallable: boolean
  isInstalled: boolean
  displayMode: 'browser' | 'standalone' | 'minimal-ui' | 'fullscreen'
  platform: string
}

export function PWAStatus({ className, serviceWorkerStatus }: PWAStatusProps) {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    online: typeof navigator !== 'undefined' ? navigator.onLine : true
  })
  
  const [installationStatus, setInstallationStatus] = useState<InstallationStatus>({
    isInstallable: false,
    isInstalled: false,
    displayMode: 'browser',
    platform: 'unknown'
  })

  // Monitor connection status
  useEffect(() => {
    if (typeof window === 'undefined') return

    const updateConnectionStatus = () => {
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection

      setConnectionStatus({
        online: navigator.onLine,
        effectiveType: connection?.effectiveType,
        downlink: connection?.downlink,
        rtt: connection?.rtt
      })
    }

    const handleOnline = () => updateConnectionStatus()
    const handleOffline = () => updateConnectionStatus()
    const handleConnectionChange = () => updateConnectionStatus()

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
    if (connection) {
      connection.addEventListener('change', handleConnectionChange)
    }

    updateConnectionStatus()

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      if (connection) {
        connection.removeEventListener('change', handleConnectionChange)
      }
    }
  }, [])

  // Monitor installation status
  useEffect(() => {
    if (typeof window === 'undefined') return

    const updateInstallationStatus = () => {
      // Check display mode
      let displayMode: InstallationStatus['displayMode'] = 'browser'

      if (window.matchMedia('(display-mode: standalone)').matches) {
        displayMode = 'standalone'
      } else if (window.matchMedia('(display-mode: minimal-ui)').matches) {
        displayMode = 'minimal-ui'
      } else if (window.matchMedia('(display-mode: fullscreen)').matches) {
        displayMode = 'fullscreen'
      }

      // Check if installed
      const isInstalled = displayMode !== 'browser' ||
                         (window.navigator as any).standalone === true ||
                         document.referrer.includes('android-app://')

      // Detect platform
      const userAgent = navigator.userAgent.toLowerCase()
      let platform = 'desktop'

      if (/android/.test(userAgent)) {
        platform = 'android'
      } else if (/iphone|ipad|ipod/.test(userAgent)) {
        platform = 'ios'
      } else if (/mobile/.test(userAgent)) {
        platform = 'mobile'
      }

      setInstallationStatus({
        isInstallable: false, // This will be updated by beforeinstallprompt event
        isInstalled,
        displayMode,
        platform
      })
    }

    const handleBeforeInstallPrompt = () => {
      setInstallationStatus(prev => ({ ...prev, isInstallable: true }))
    }

    const handleAppInstalled = () => {
      setInstallationStatus(prev => ({ 
        ...prev, 
        isInstalled: true, 
        isInstallable: false,
        displayMode: 'standalone'
      }))
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    updateInstallationStatus()

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  const getConnectionInfo = () => {
    if (!connectionStatus.online) {
      return {
        icon: WifiOff,
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        badge: 'Offline',
        badgeVariant: 'destructive' as const,
        description: 'No internet connection available.'
      }
    }

    const speed = connectionStatus.effectiveType
    let speedColor = 'text-green-600'
    let speedBg = 'bg-green-50'
    let speedBorder = 'border-green-200'

    if (speed === 'slow-2g' || speed === '2g') {
      speedColor = 'text-red-600'
      speedBg = 'bg-red-50'
      speedBorder = 'border-red-200'
    } else if (speed === '3g') {
      speedColor = 'text-yellow-600'
      speedBg = 'bg-yellow-50'
      speedBorder = 'border-yellow-200'
    }

    return {
      icon: Wifi,
      color: speedColor,
      bgColor: speedBg,
      borderColor: speedBorder,
      badge: 'Online',
      badgeVariant: 'default' as const,
      description: `Connected${speed ? ` (${speed.toUpperCase()})` : ''}`
    }
  }

  const getInstallationInfo = () => {
    if (installationStatus.isInstalled) {
      return {
        icon: Smartphone,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        badge: 'Installed',
        badgeVariant: 'default' as const,
        description: `Running in ${installationStatus.displayMode} mode`
      }
    }

    if (installationStatus.isInstallable) {
      return {
        icon: Monitor,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
        badge: 'Installable',
        badgeVariant: 'default' as const,
        description: 'App can be installed on this device'
      }
    }

    return {
      icon: Globe,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200',
      badge: 'Web App',
      badgeVariant: 'secondary' as const,
      description: 'Running as web application'
    }
  }

  const getServiceWorkerInfo = () => {
    if (!serviceWorkerStatus) {
      return {
        icon: Settings,
        color: 'text-gray-600',
        badge: 'Unknown',
        badgeVariant: 'secondary' as const,
        description: 'Service Worker status unknown'
      }
    }

    switch (serviceWorkerStatus.state) {
      case 'activated':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          badge: 'Active',
          badgeVariant: 'default' as const,
          description: 'Service Worker is running'
        }
      case 'installing':
      case 'activating':
        return {
          icon: Clock,
          color: 'text-yellow-600',
          badge: 'Installing',
          badgeVariant: 'default' as const,
          description: 'Service Worker is being installed'
        }
      case 'error':
        return {
          icon: AlertCircle,
          color: 'text-red-600',
          badge: 'Error',
          badgeVariant: 'destructive' as const,
          description: 'Service Worker error'
        }
      case 'not-supported':
        return {
          icon: AlertCircle,
          color: 'text-red-600',
          badge: 'Not Supported',
          badgeVariant: 'destructive' as const,
          description: 'Service Workers not supported'
        }
      default:
        return {
          icon: Settings,
          color: 'text-gray-600',
          badge: 'Inactive',
          badgeVariant: 'secondary' as const,
          description: 'Service Worker not active'
        }
    }
  }

  const connectionInfo = getConnectionInfo()
  const installationInfo = getInstallationInfo()
  const serviceWorkerInfo = getServiceWorkerInfo()

  const ConnectionIcon = connectionInfo.icon
  const InstallationIcon = installationInfo.icon
  const ServiceWorkerIcon = serviceWorkerInfo.icon

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          PWA Status
        </CardTitle>
        <CardDescription>
          Current status of Progressive Web App features
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Connection Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <ConnectionIcon className={cn("h-4 w-4", connectionInfo.color)} />
            <div>
              <p className="text-sm font-medium">Connection</p>
              <p className="text-xs text-muted-foreground">{connectionInfo.description}</p>
            </div>
          </div>
          <Badge variant={connectionInfo.badgeVariant}>
            {connectionInfo.badge}
          </Badge>
        </div>

        <Separator />

        {/* Installation Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <InstallationIcon className={cn("h-4 w-4", installationInfo.color)} />
            <div>
              <p className="text-sm font-medium">Installation</p>
              <p className="text-xs text-muted-foreground">{installationInfo.description}</p>
            </div>
          </div>
          <Badge variant={installationInfo.badgeVariant}>
            {installationInfo.badge}
          </Badge>
        </div>

        <Separator />

        {/* Service Worker Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <ServiceWorkerIcon className={cn("h-4 w-4", serviceWorkerInfo.color)} />
            <div>
              <p className="text-sm font-medium">Service Worker</p>
              <p className="text-xs text-muted-foreground">{serviceWorkerInfo.description}</p>
            </div>
          </div>
          <Badge variant={serviceWorkerInfo.badgeVariant}>
            {serviceWorkerInfo.badge}
          </Badge>
        </div>

        {/* Additional Info */}
        {(connectionStatus.downlink || connectionStatus.rtt) && (
          <>
            <Separator />
            <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
              {connectionStatus.downlink && (
                <div>
                  <span className="font-medium">Speed:</span> {connectionStatus.downlink} Mbps
                </div>
              )}
              {connectionStatus.rtt && (
                <div>
                  <span className="font-medium">Latency:</span> {connectionStatus.rtt}ms
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
