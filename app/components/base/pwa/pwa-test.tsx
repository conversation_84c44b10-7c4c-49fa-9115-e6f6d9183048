import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { Badge } from "~/components/ui/badge"
import { Alert, AlertDescription } from "~/components/ui/alert"
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Wifi, 
  WifiOff,
  Download,
  Settings,
  TestTube
} from "lucide-react"
import { cn } from "~/lib/utils"

interface TestResult {
  name: string
  status: 'pass' | 'fail' | 'warning' | 'pending'
  message: string
  details?: string
}

export interface PWATestProps {
  className?: string
}

export function PWATest({ className }: PWATestProps) {
  const [tests, setTests] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [overallStatus, setOverallStatus] = useState<'pass' | 'fail' | 'warning' | 'pending'>('pending')

  const runTests = useCallback(async () => {
    setIsRunning(true)
    const results: TestResult[] = []

    // Test 1: Service Worker Support
    try {
      if ('serviceWorker' in navigator) {
        results.push({
          name: 'Service Worker Support',
          status: 'pass',
          message: 'Service Workers are supported',
          details: 'Browser supports Service Worker API'
        })
      } else {
        results.push({
          name: 'Service Worker Support',
          status: 'fail',
          message: 'Service Workers not supported',
          details: 'Browser does not support Service Worker API'
        })
      }
    } catch (error) {
      results.push({
        name: 'Service Worker Support',
        status: 'fail',
        message: 'Error checking Service Worker support',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Test 2: Service Worker Registration
    try {
      const registration = await navigator.serviceWorker.getRegistration()
      if (registration) {
        results.push({
          name: 'Service Worker Registration',
          status: 'pass',
          message: 'Service Worker is registered',
          details: `Scope: ${registration.scope}`
        })
      } else {
        results.push({
          name: 'Service Worker Registration',
          status: 'warning',
          message: 'Service Worker not registered yet',
          details: 'Registration may be in progress'
        })
      }
    } catch (error) {
      results.push({
        name: 'Service Worker Registration',
        status: 'fail',
        message: 'Failed to check Service Worker registration',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Test 3: Manifest
    try {
      const response = await fetch('/manifest.json')
      if (response.ok) {
        const manifest = await response.json()
        results.push({
          name: 'Web App Manifest',
          status: 'pass',
          message: 'Manifest is accessible',
          details: `Name: ${manifest.name || 'Unknown'}`
        })
      } else {
        results.push({
          name: 'Web App Manifest',
          status: 'fail',
          message: 'Manifest not accessible',
          details: `HTTP ${response.status}: ${response.statusText}`
        })
      }
    } catch (error) {
      results.push({
        name: 'Web App Manifest',
        status: 'fail',
        message: 'Failed to fetch manifest',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Test 4: Cache API
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        results.push({
          name: 'Cache API',
          status: 'pass',
          message: 'Cache API is available',
          details: `${cacheNames.length} cache(s) found: ${cacheNames.join(', ')}`
        })
      } else {
        results.push({
          name: 'Cache API',
          status: 'fail',
          message: 'Cache API not supported',
          details: 'Browser does not support Cache API'
        })
      }
    } catch (error) {
      results.push({
        name: 'Cache API',
        status: 'fail',
        message: 'Error checking Cache API',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Test 5: Install Prompt
    try {
      const isInstallable = window.matchMedia('(display-mode: browser)').matches
      if (isInstallable) {
        results.push({
          name: 'Install Prompt',
          status: 'pass',
          message: 'App is installable',
          details: 'Running in browser mode, can be installed'
        })
      } else {
        results.push({
          name: 'Install Prompt',
          status: 'warning',
          message: 'App may already be installed',
          details: 'Running in standalone mode or already installed'
        })
      }
    } catch (error) {
      results.push({
        name: 'Install Prompt',
        status: 'fail',
        message: 'Error checking install status',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Test 6: Offline Support
    try {
      const offlineResponse = await fetch('/offline.html', { cache: 'no-cache' })
      if (offlineResponse.ok) {
        results.push({
          name: 'Offline Support',
          status: 'pass',
          message: 'Offline page is available',
          details: 'Offline fallback page exists'
        })
      } else {
        results.push({
          name: 'Offline Support',
          status: 'warning',
          message: 'Offline page not found',
          details: 'No offline fallback page available'
        })
      }
    } catch (error) {
      results.push({
        name: 'Offline Support',
        status: 'fail',
        message: 'Error checking offline support',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    setTests(results)

    // Calculate overall status
    const hasFailures = results.some(test => test.status === 'fail')
    const hasWarnings = results.some(test => test.status === 'warning')
    
    if (hasFailures) {
      setOverallStatus('fail')
    } else if (hasWarnings) {
      setOverallStatus('warning')
    } else {
      setOverallStatus('pass')
    }

    setIsRunning(false)
  }, []) // Empty dependency array since runTests doesn't depend on any props/state

  useEffect(() => {
    // Auto-run tests on mount
    if (typeof window !== 'undefined') {
      runTests()
    }
  }, [runTests])

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'fail':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <Badge variant="default" className="bg-green-600">Pass</Badge>
      case 'fail':
        return <Badge variant="destructive">Fail</Badge>
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-600 text-white">Warning</Badge>
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>
    }
  }

  const getOverallStatusInfo = () => {
    switch (overallStatus) {
      case 'pass':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          message: 'All PWA tests passed successfully!'
        }
      case 'fail':
        return {
          icon: AlertCircle,
          color: 'text-red-600',
          message: 'Some PWA tests failed. Check the details below.'
        }
      case 'warning':
        return {
          icon: AlertCircle,
          color: 'text-yellow-600',
          message: 'PWA tests completed with warnings.'
        }
      case 'pending':
        return {
          icon: Clock,
          color: 'text-gray-600',
          message: 'PWA tests are pending...'
        }
    }
  }

  const overallInfo = getOverallStatusInfo()
  const OverallIcon = overallInfo.icon

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            <CardTitle>PWA Compatibility Test</CardTitle>
          </div>
          <Button 
            onClick={runTests} 
            disabled={isRunning}
            size="sm"
            variant="outline"
          >
            {isRunning ? (
              <>
                <div className="mr-2 h-3 w-3 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Testing...
              </>
            ) : (
              <>
                <TestTube className="mr-2 h-3 w-3" />
                Run Tests
              </>
            )}
          </Button>
        </div>
        <CardDescription>
          Verify PWA functionality and browser compatibility
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Status */}
        <Alert className={cn(
          overallStatus === 'pass' && 'border-green-200 bg-green-50',
          overallStatus === 'fail' && 'border-red-200 bg-red-50',
          overallStatus === 'warning' && 'border-yellow-200 bg-yellow-50'
        )}>
          <OverallIcon className={cn("h-4 w-4", overallInfo.color)} />
          <AlertDescription className={overallInfo.color}>
            {overallInfo.message}
          </AlertDescription>
        </Alert>

        {/* Test Results */}
        <div className="space-y-3">
          {tests.map((test, index) => (
            <div key={index} className="flex items-start justify-between p-3 border rounded-lg">
              <div className="flex items-start gap-3">
                {getStatusIcon(test.status)}
                <div>
                  <p className="font-medium">{test.name}</p>
                  <p className="text-sm text-muted-foreground">{test.message}</p>
                  {test.details && (
                    <p className="text-xs text-muted-foreground mt-1">{test.details}</p>
                  )}
                </div>
              </div>
              {getStatusBadge(test.status)}
            </div>
          ))}
        </div>

        {tests.length === 0 && !isRunning && (
          <div className="text-center py-8 text-muted-foreground">
            <TestTube className="h-8 w-8 mx-auto mb-2" />
            <p>Click "Run Tests" to check PWA compatibility</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
