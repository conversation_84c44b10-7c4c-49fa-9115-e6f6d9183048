import { useState, useEffect } from "react"
import { But<PERSON> } from "~/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { Badge } from "~/components/ui/badge"
import { Alert, AlertDescription } from "~/components/ui/alert"
import { RefreshC<PERSON>, Settings, AlertCircle, CheckCircle, Clock } from "lucide-react"
import { cn } from "~/lib/utils"

export type ServiceWorkerState = 
  | 'not-supported'
  | 'not-registered'
  | 'registering'
  | 'registered'
  | 'installing'
  | 'installed'
  | 'activating'
  | 'activated'
  | 'redundant'
  | 'error'

export interface ServiceWorkerStatus {
  state: ServiceWorkerState
  registration: ServiceWorkerRegistration | null
  worker: ServiceWorker | null
  updateAvailable: boolean
  error: string | null
}

export interface ServiceWorkerManagerProps {
  className?: string
  onStateChange?: (status: ServiceWorkerStatus) => void
  onUpdateAvailable?: () => void
  onUpdateInstalled?: () => void
}

export function ServiceWorkerManager({ 
  className, 
  onStateChange, 
  onUpdateAvailable, 
  onUpdateInstalled 
}: ServiceWorkerManagerProps) {
  const [status, setStatus] = useState<ServiceWorkerStatus>({
    state: 'not-supported',
    registration: null,
    worker: null,
    updateAvailable: false,
    error: null
  })
  const [isUpdating, setIsUpdating] = useState(false)

  const updateStatus = (newStatus: Partial<ServiceWorkerStatus>) => {
    setStatus(prev => {
      const updated = { ...prev, ...newStatus }
      onStateChange?.(updated)
      return updated
    })
  }

  useEffect(() => {
    if (!('serviceWorker' in navigator)) {
      updateStatus({ state: 'not-supported' })
      return
    }

    const registerServiceWorker = async () => {
      try {
        updateStatus({ state: 'registering' })

        const registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/'
        })

        updateStatus({ 
          state: 'registered', 
          registration,
          error: null
        })

        // Listen for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing
          if (!newWorker) return

          updateStatus({ 
            state: 'installing',
            worker: newWorker
          })

          newWorker.addEventListener('statechange', () => {
            switch (newWorker.state) {
              case 'installed':
                if (navigator.serviceWorker.controller) {
                  // New update available
                  updateStatus({ 
                    state: 'installed',
                    updateAvailable: true
                  })
                  onUpdateAvailable?.()
                } else {
                  // First install
                  updateStatus({ state: 'installed' })
                  onUpdateInstalled?.()
                }
                break
              case 'activating':
                updateStatus({ state: 'activating' })
                break
              case 'activated':
                updateStatus({ state: 'activated' })
                break
              case 'redundant':
                updateStatus({ state: 'redundant' })
                break
            }
          })
        })

        // Check for existing service worker
        if (registration.active) {
          updateStatus({ 
            state: 'activated',
            worker: registration.active
          })
        }

        // Check for waiting service worker (update available)
        if (registration.waiting) {
          updateStatus({ 
            updateAvailable: true,
            worker: registration.waiting
          })
          onUpdateAvailable?.()
        }

      } catch (error) {
        console.error('Service Worker registration failed:', error)
        updateStatus({ 
          state: 'error', 
          error: error instanceof Error ? error.message : 'Registration failed'
        })
      }
    }

    registerServiceWorker()

    // Listen for controller changes (new SW activated)
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      updateStatus({ state: 'activated' })
      window.location.reload()
    })

  }, [onStateChange, onUpdateAvailable, onUpdateInstalled])

  const handleUpdate = async () => {
    if (!status.registration || !status.registration.waiting) return

    setIsUpdating(true)

    try {
      // Tell the waiting service worker to skip waiting
      status.registration.waiting.postMessage({ type: 'SKIP_WAITING' })
    } catch (error) {
      console.error('Failed to update service worker:', error)
    } finally {
      setIsUpdating(false)
    }
  }

  const handleRefresh = () => {
    window.location.reload()
  }

  const getStateInfo = () => {
    switch (status.state) {
      case 'not-supported':
        return {
          icon: AlertCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          badge: 'Not Supported',
          badgeVariant: 'destructive' as const,
          description: 'Service Workers are not supported in this browser.'
        }
      case 'not-registered':
        return {
          icon: AlertCircle,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          badge: 'Not Registered',
          badgeVariant: 'secondary' as const,
          description: 'Service Worker is not registered.'
        }
      case 'registering':
        return {
          icon: Clock,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          badge: 'Registering',
          badgeVariant: 'default' as const,
          description: 'Registering Service Worker...'
        }
      case 'registered':
      case 'installing':
      case 'activating':
        return {
          icon: Clock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          badge: 'Installing',
          badgeVariant: 'default' as const,
          description: 'Service Worker is being installed...'
        }
      case 'installed':
      case 'activated':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          badge: 'Active',
          badgeVariant: 'default' as const,
          description: 'Service Worker is active and running.'
        }
      case 'error':
        return {
          icon: AlertCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          badge: 'Error',
          badgeVariant: 'destructive' as const,
          description: status.error || 'An error occurred with the Service Worker.'
        }
      default:
        return {
          icon: Settings,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          badge: 'Unknown',
          badgeVariant: 'secondary' as const,
          description: 'Service Worker status is unknown.'
        }
    }
  }

  const stateInfo = getStateInfo()
  const Icon = stateInfo.icon

  return (
    <Card className={cn(stateInfo.borderColor, stateInfo.bgColor, className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Icon className={cn("h-5 w-5", stateInfo.color)} />
            <CardTitle className={stateInfo.color}>Service Worker</CardTitle>
          </div>
          <Badge variant={stateInfo.badgeVariant}>
            {stateInfo.badge}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <CardDescription className={stateInfo.color}>
          {stateInfo.description}
        </CardDescription>

        {status.updateAvailable && (
          <Alert>
            <RefreshCw className="h-4 w-4" />
            <AlertDescription>
              A new version is available. Update to get the latest features.
            </AlertDescription>
          </Alert>
        )}

        <div className="flex gap-2">
          {status.updateAvailable && (
            <Button 
              onClick={handleUpdate} 
              disabled={isUpdating}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isUpdating ? (
                <>
                  <div className="mr-2 h-3 w-3 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Updating...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-3 w-3" />
                  Update
                </>
              )}
            </Button>
          )}
          
          <Button 
            onClick={handleRefresh} 
            variant="outline"
            size="sm"
          >
            <RefreshCw className="mr-2 h-3 w-3" />
            Refresh
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
