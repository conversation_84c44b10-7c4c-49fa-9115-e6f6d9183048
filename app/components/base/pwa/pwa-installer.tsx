import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { Badge } from "~/components/ui/badge"
import { Download, Smartphone, X } from "lucide-react"
import { cn } from "~/lib/utils"

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[]
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed'
    platform: string
  }>
  prompt(): Promise<void>
}

declare global {
  interface WindowEventMap {
    beforeinstallprompt: BeforeInstallPromptEvent
  }
}

export interface PWAInstallerProps {
  className?: string
  showCard?: boolean
  onInstallSuccess?: () => void
  onInstallError?: (error: Error) => void
}

export function PWAInstaller({ 
  className, 
  showCard = true, 
  onInstallSuccess, 
  onInstallError 
}: PWAInstallerProps) {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [isInstallable, setIsInstallable] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [isInstalling, setIsInstalling] = useState(false)
  const [showPrompt, setShowPrompt] = useState(false)

  useEffect(() => {
    // Check if we're in the browser environment
    if (typeof window === 'undefined' || typeof navigator === 'undefined') {
      return
    }

    // Check if app is already installed
    const checkInstalled = () => {
      if (window.matchMedia('(display-mode: standalone)').matches) {
        setIsInstalled(true)
        return
      }

      if ((window.navigator as any).standalone === true) {
        setIsInstalled(true)
        return
      }
      
      if (document.referrer.includes('android-app://')) {
        setIsInstalled(true)
        return
      }
    }

    checkInstalled()

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      e.preventDefault()
      setDeferredPrompt(e)
      setIsInstallable(true)
      setShowPrompt(true)
    }

    // Listen for appinstalled event
    const handleAppInstalled = () => {
      setIsInstalled(true)
      setIsInstallable(false)
      setShowPrompt(false)
      setDeferredPrompt(null)
      onInstallSuccess?.()
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [onInstallSuccess])

  const handleInstall = async () => {
    if (!deferredPrompt) return

    setIsInstalling(true)

    try {
      await deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        console.log('PWA installation accepted')
      } else {
        console.log('PWA installation dismissed')
      }
      
      setDeferredPrompt(null)
      setIsInstallable(false)
      setShowPrompt(false)
    } catch (error) {
      console.error('PWA installation failed:', error)
      onInstallError?.(error as Error)
    } finally {
      setIsInstalling(false)
    }
  }

  const handleDismiss = () => {
    setShowPrompt(false)
  }

  if (isInstalled) {
    return showCard ? (
      <Card className={cn("border-green-200 bg-green-50", className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Smartphone className="h-5 w-5 text-green-600" />
              <CardTitle className="text-green-800">App Installed</CardTitle>
            </div>
            <Badge variant="default" className="bg-green-500">
              Installed
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <CardDescription className="text-green-700">
            The app is successfully installed and running in standalone mode.
          </CardDescription>
        </CardContent>
      </Card>
    ) : null
  }

  if (!isInstallable || !showPrompt) {
    return showCard ? (
      <Card className={cn("border-gray-200", className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Download className="h-5 w-5 text-gray-500" />
              <CardTitle className="text-gray-700">PWA Installation</CardTitle>
            </div>
            <Badge variant="secondary">
              Not Available
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <CardDescription>
            PWA installation is not available on this device or browser.
          </CardDescription>
        </CardContent>
      </Card>
    ) : null
  }

  return showCard ? (
    <Card className={cn("border-blue-200 bg-blue-50", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Download className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-blue-800">Install App</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="default" className="bg-blue-500">
              Available
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="h-6 w-6 p-0 text-blue-600 hover:bg-blue-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <CardDescription className="text-blue-700">
          Install this app on your device for a better experience with offline access and native features.
        </CardDescription>
        <Button 
          onClick={handleInstall} 
          disabled={isInstalling}
          className="w-full bg-blue-600 hover:bg-blue-700"
        >
          {isInstalling ? (
            <>
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
              Installing...
            </>
          ) : (
            <>
              <Download className="mr-2 h-4 w-4" />
              Install App
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  ) : (
    <Button 
      onClick={handleInstall} 
      disabled={isInstalling}
      className={cn("bg-blue-600 hover:bg-blue-700", className)}
    >
      {isInstalling ? (
        <>
          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
          Installing...
        </>
      ) : (
        <>
          <Download className="mr-2 h-4 w-4" />
          Install App
        </>
      )}
    </Button>
  )
}
