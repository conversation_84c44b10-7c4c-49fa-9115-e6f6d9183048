import type { Route } from "./+types/pwa-status-page";
import { useState, useEffect } from "react"
import { AppLayout } from "~/components/layouts/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { Badge } from "~/components/ui/badge"
import { Alert, AlertDescription } from "~/components/ui/alert"
import { 
  Smartphone, 
  CheckCircle, 
  AlertCircle, 
  Info,
  Wifi,
  WifiOff,
  Download,
  Bell,
  Shield,
  Globe,
  Settings,
  Monitor
} from "lucide-react"
import { generateBreadcrumbs } from "~/config/navigation"
import { PWAStatus, PwaAnalyzer } from './index'

export function meta({}: Route.MetaArgs) {
  return [
    { title: "PWA Status - Admin Panel" },
    { name: "description", content: "Detailed Progressive Web App status monitoring and information" },
  ];
}

export default function PWAStatusPage() {
  const [isAnalyzerOpen, setIsAnalyzerOpen] = useState(false)
  const [isOnline, setIsOnline] = useState(true)
  const [isPWA, setIsPWA] = useState(false)
  const [browserInfo, setBrowserInfo] = useState<any>({})

  const breadcrumbItems = generateBreadcrumbs("/pwa-status");

  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Check online status
      setIsOnline(navigator.onLine)
      const updateOnlineStatus = () => setIsOnline(navigator.onLine)
      window.addEventListener('online', updateOnlineStatus)
      window.addEventListener('offline', updateOnlineStatus)

      // Check if running as PWA
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches || 
                          (window.navigator as any).standalone === true
      setIsPWA(isStandalone)

      // Get browser info
      setBrowserInfo({
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        serviceWorkerSupported: 'serviceWorker' in navigator,
        notificationSupported: 'Notification' in window,
        geolocationSupported: 'geolocation' in navigator
      })

      return () => {
        window.removeEventListener('online', updateOnlineStatus)
        window.removeEventListener('offline', updateOnlineStatus)
      }
    }
  }, [])

  const statusItems = [
    {
      icon: isOnline ? Wifi : WifiOff,
      title: "Connection Status",
      value: isOnline ? "Online" : "Offline",
      status: isOnline ? "success" : "error",
      description: isOnline ? "Connected to the internet" : "No internet connection"
    },
    {
      icon: isPWA ? Smartphone : Monitor,
      title: "App Mode",
      value: isPWA ? "PWA Mode" : "Browser Mode",
      status: isPWA ? "success" : "info",
      description: isPWA ? "Running as installed PWA" : "Running in web browser"
    },
    {
      icon: browserInfo.serviceWorkerSupported ? CheckCircle : AlertCircle,
      title: "Service Worker",
      value: browserInfo.serviceWorkerSupported ? "Supported" : "Not Supported",
      status: browserInfo.serviceWorkerSupported ? "success" : "error",
      description: browserInfo.serviceWorkerSupported ? "Service Worker API available" : "Service Worker not supported"
    },
    {
      icon: browserInfo.notificationSupported ? Bell : AlertCircle,
      title: "Notifications",
      value: browserInfo.notificationSupported ? "Supported" : "Not Supported",
      status: browserInfo.notificationSupported ? "success" : "warning",
      description: browserInfo.notificationSupported ? "Notification API available" : "Notifications not supported"
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'warning': return 'text-yellow-600'
      default: return 'text-blue-600'
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success': return <Badge className="bg-green-600">Active</Badge>
      case 'error': return <Badge variant="destructive">Error</Badge>
      case 'warning': return <Badge variant="secondary" className="bg-yellow-600 text-white">Warning</Badge>
      default: return <Badge variant="secondary">Info</Badge>
    }
  }

  return (
    <AppLayout
      title="PWA Status"
      breadcrumbItems={breadcrumbItems}
      className="gap-6 p-6 pt-0"
    >
      {/* Header Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">PWA Status</h1>
            <p className="text-muted-foreground">
              Detailed status information and monitoring for your Progressive Web App
            </p>
          </div>
          <div className="flex items-center gap-2">
            <PWAStatus onAnalyzeClick={() => setIsAnalyzerOpen(true)} />
            <Button 
              variant="outline"
              onClick={() => setIsAnalyzerOpen(true)}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              Analyze
            </Button>
          </div>
        </div>
      </div>

      {/* Status Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statusItems.map((item, index) => {
          const Icon = item.icon
          return (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <Icon className={`h-5 w-5 ${getStatusColor(item.status)}`} />
                  {getStatusBadge(item.status)}
                </div>
                <div className="space-y-1">
                  <h3 className="font-medium">{item.title}</h3>
                  <p className="text-sm font-semibold">{item.value}</p>
                  <p className="text-xs text-muted-foreground">{item.description}</p>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Detailed Information */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Browser Information
            </CardTitle>
            <CardDescription>
              Current browser and environment details
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Platform:</span>
                <span className="text-sm font-medium">{browserInfo.platform}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Language:</span>
                <span className="text-sm font-medium">{browserInfo.language}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Cookies:</span>
                <span className="text-sm font-medium">
                  {browserInfo.cookieEnabled ? 'Enabled' : 'Disabled'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Geolocation:</span>
                <span className="text-sm font-medium">
                  {browserInfo.geolocationSupported ? 'Supported' : 'Not Supported'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              PWA Capabilities
            </CardTitle>
            <CardDescription>
              Available Progressive Web App features
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Service Worker</span>
                {browserInfo.serviceWorkerSupported ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-600" />
                )}
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Push Notifications</span>
                {browserInfo.notificationSupported ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-600" />
                )}
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Offline Support</span>
                {browserInfo.serviceWorkerSupported ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-600" />
                )}
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">App Installation</span>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Alerts */}
      <div className="space-y-4">
        {!isOnline && (
          <Alert variant="destructive">
            <WifiOff className="h-4 w-4" />
            <AlertDescription>
              <strong>Offline Mode:</strong> You are currently offline. Some features may be limited.
            </AlertDescription>
          </Alert>
        )}

        {isPWA && (
          <Alert>
            <Smartphone className="h-4 w-4" />
            <AlertDescription>
              <strong>PWA Mode Active:</strong> You are running this application as an installed Progressive Web App.
            </AlertDescription>
          </Alert>
        )}

        {!browserInfo.serviceWorkerSupported && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Limited PWA Support:</strong> Your browser does not support Service Workers, which limits PWA functionality.
            </AlertDescription>
          </Alert>
        )}
      </div>

      {/* PWA Analyzer Dialog */}
      <PwaAnalyzer 
        open={isAnalyzerOpen} 
        onOpenChange={setIsAnalyzerOpen} 
      />
    </AppLayout>
  );
}
