import { useState, useCallback } from "react"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { Badge } from "~/components/ui/badge"
import { Alert, AlertDescription } from "~/components/ui/alert"
import { 
  Bell, 
  BellOff, 
  CheckCircle, 
  AlertCircle, 
  Info,
  TestTube
} from "lucide-react"
import { cn } from "~/lib/utils"

export interface NotificationTestProps {
  className?: string
}

export function NotificationTest({ className }: NotificationTestProps) {
  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [isRequesting, setIsRequesting] = useState(false)
  const [lastNotification, setLastNotification] = useState<string | null>(null)

  // Update permission state
  const updatePermission = useCallback(() => {
    if ('Notification' in window) {
      setPermission(Notification.permission)
    }
  }, [])

  // Request notification permission
  const requestPermission = useCallback(async () => {
    if (!('Notification' in window)) {
      return
    }

    setIsRequesting(true)
    
    try {
      const result = await Notification.requestPermission()
      setPermission(result)
      
      if (result === 'granted') {
        setLastNotification('Permission granted successfully!')
      } else if (result === 'denied') {
        setLastNotification('Permission denied by user')
      } else {
        setLastNotification('Permission request dismissed')
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error)
      setLastNotification('Error requesting permission')
    } finally {
      setIsRequesting(false)
    }
  }, [])

  // Send test notification
  const sendTestNotification = useCallback(() => {
    if (!('Notification' in window) || Notification.permission !== 'granted') {
      return
    }

    const notification = new Notification('PWA Test Notification', {
      body: 'This is a test notification from your PWA!',
      icon: '/icon-192x192.png',
      badge: '/icon-192x192.png',
      tag: 'pwa-test',
      requireInteraction: false,
      silent: false,
      vibrate: [100, 50, 100],
      data: {
        timestamp: Date.now(),
        source: 'pwa-demo'
      }
    })

    notification.onclick = () => {
      window.focus()
      notification.close()
    }

    notification.onshow = () => {
      setLastNotification('Test notification sent successfully!')
    }

    notification.onerror = () => {
      setLastNotification('Error sending notification')
    }

    // Auto-close after 5 seconds
    setTimeout(() => {
      notification.close()
    }, 5000)
  }, [])

  // Check if notifications are supported
  const isSupported = 'Notification' in window

  const getPermissionInfo = () => {
    switch (permission) {
      case 'granted':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          title: 'Permission Granted',
          description: 'Notifications are enabled and working',
          badge: 'Enabled',
          badgeVariant: 'default' as const
        }
      case 'denied':
        return {
          icon: BellOff,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          title: 'Permission Denied',
          description: 'Notifications are blocked. Enable them in browser settings.',
          badge: 'Blocked',
          badgeVariant: 'destructive' as const
        }
      case 'default':
        return {
          icon: Bell,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          title: 'Permission Required',
          description: 'Click the button below to enable notifications',
          badge: 'Not Requested',
          badgeVariant: 'secondary' as const
        }
      default:
        return {
          icon: AlertCircle,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          title: 'Unknown Status',
          description: 'Unable to determine notification permission status',
          badge: 'Unknown',
          badgeVariant: 'secondary' as const
        }
    }
  }

  const info = getPermissionInfo()
  const Icon = info.icon

  // Update permission on mount and when it changes
  useState(() => {
    if (typeof window !== 'undefined') {
      updatePermission()
    }
  })

  if (!isSupported) {
    return (
      <Card className={cn("border-red-200 bg-red-50", className)}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BellOff className="h-5 w-5 text-red-600" />
              <CardTitle className="text-red-600">Notifications Not Supported</CardTitle>
            </div>
            <Badge variant="destructive">Not Available</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <CardDescription className="text-red-600">
            This browser does not support the Notification API. Try using a modern browser like Chrome, Firefox, or Edge.
          </CardDescription>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn(info.borderColor, info.bgColor, className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            <CardTitle>Notification Test</CardTitle>
          </div>
          <Badge variant={info.badgeVariant}>{info.badge}</Badge>
        </div>
        <CardDescription>
          Test push notification functionality and permissions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Permission Status */}
        <div className={cn("p-4 rounded-lg border", info.borderColor, info.bgColor)}>
          <div className="flex items-center gap-3 mb-2">
            <Icon className={cn("h-5 w-5", info.color)} />
            <h4 className={cn("font-medium", info.color)}>{info.title}</h4>
          </div>
          <p className={cn("text-sm", info.color)}>{info.description}</p>
        </div>

        {/* Last Notification Status */}
        {lastNotification && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>{lastNotification}</AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          {permission === 'default' && (
            <Button 
              onClick={requestPermission} 
              disabled={isRequesting}
              className="flex-1"
            >
              {isRequesting ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Requesting...
                </>
              ) : (
                <>
                  <Bell className="mr-2 h-4 w-4" />
                  Request Permission
                </>
              )}
            </Button>
          )}

          {permission === 'granted' && (
            <Button onClick={sendTestNotification} className="flex-1">
              <Bell className="mr-2 h-4 w-4" />
              Send Test Notification
            </Button>
          )}

          {permission === 'denied' && (
            <Button variant="outline" onClick={updatePermission} className="flex-1">
              <Bell className="mr-2 h-4 w-4" />
              Check Permission
            </Button>
          )}
        </div>

        {/* Instructions */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>Note:</strong> Notifications require user permission and HTTPS (or localhost).</p>
          {permission === 'denied' && (
            <p><strong>To enable:</strong> Click the lock icon in your browser's address bar and allow notifications.</p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
