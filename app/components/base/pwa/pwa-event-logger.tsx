import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { Button } from "~/components/ui/button"
import { Badge } from "~/components/ui/badge"
// ScrollArea component not available, using div with overflow
import { Separator } from "~/components/ui/separator"
import { 
  Activity, 
  Trash2, 
  Download,
  Wifi,
  WifiOff,
  Smartphone,
  Settings,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react"
import { cn } from "~/lib/utils"

export type PWAEventType = 
  | 'install'
  | 'app-installed'
  | 'sw-registered'
  | 'sw-installing'
  | 'sw-installed'
  | 'sw-activating'
  | 'sw-activated'
  | 'sw-error'
  | 'sw-update-available'
  | 'sw-update-installed'
  | 'network-online'
  | 'network-offline'
  | 'network-change'
  | 'page-load'
  | 'page-unload'
  | 'visibility-change'

export interface PWAEvent {
  id: string
  type: PWAEventType
  timestamp: Date
  message: string
  data?: any
  level: 'info' | 'warning' | 'error' | 'success'
}

export interface PWAEventLoggerProps {
  className?: string
  maxEvents?: number
  autoScroll?: boolean
  showTimestamps?: boolean
  onEventLogged?: (event: PWAEvent) => void
}

export function PWAEventLogger({ 
  className, 
  maxEvents = 100, 
  autoScroll = true,
  showTimestamps = true,
  onEventLogged 
}: PWAEventLoggerProps) {
  const [events, setEvents] = useState<PWAEvent[]>([])
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const eventIdCounter = useRef(0)

  const logEvent = (
    type: PWAEventType, 
    message: string, 
    level: PWAEvent['level'] = 'info',
    data?: any
  ) => {
    const event: PWAEvent = {
      id: `event-${++eventIdCounter.current}`,
      type,
      timestamp: new Date(),
      message,
      level,
      data
    }

    setEvents(prev => {
      const newEvents = [event, ...prev].slice(0, maxEvents)
      return newEvents
    })

    onEventLogged?.(event)

    if (autoScroll && scrollAreaRef.current) {
      setTimeout(() => {
        scrollAreaRef.current?.scrollTo({ top: 0, behavior: 'smooth' })
      }, 100)
    }
  }

  useEffect(() => {
    // Check if we're in the browser environment
    if (typeof window === 'undefined' || typeof navigator === 'undefined') {
      return
    }

    // Log initial page load
    logEvent('page-load', 'Page loaded', 'info')

    // Service Worker events
    if ('serviceWorker' in navigator) {
      // Check if already registered
      navigator.serviceWorker.getRegistration()
        .then(existingRegistration => {
          if (existingRegistration) {
            logEvent('sw-registered', 'Service Worker already registered', 'info', {
              scope: existingRegistration.scope,
              state: existingRegistration.active?.state
            })

            // Check current state
            if (existingRegistration.active) {
              logEvent('sw-activated', 'Service Worker is active', 'success', {
                scriptURL: existingRegistration.active.scriptURL
              })
            }

            if (existingRegistration.waiting) {
              logEvent('sw-update-available', 'Service Worker update is waiting', 'info')
            }

            if (existingRegistration.installing) {
              logEvent('sw-installing', 'Service Worker is installing', 'info')
            }
          }
        })

      navigator.serviceWorker.register('/sw.js', { scope: '/' })
        .then(registration => {
          logEvent('sw-registered', 'Service Worker registration initiated', 'success', {
            scope: registration.scope
          })

          // Listen for updates
          registration.addEventListener('updatefound', () => {
            logEvent('sw-update-available', 'Service Worker update found', 'info')

            const newWorker = registration.installing
            if (newWorker) {
              logEvent('sw-installing', 'New Service Worker installing', 'info', {
                scriptURL: newWorker.scriptURL
              })

              newWorker.addEventListener('statechange', () => {
                logEvent('sw-state-change', `Service Worker state changed to: ${newWorker.state}`, 'info', {
                  state: newWorker.state,
                  scriptURL: newWorker.scriptURL
                })

                switch (newWorker.state) {
                  case 'installed':
                    if (navigator.serviceWorker.controller) {
                      logEvent('sw-update-installed', 'Service Worker update installed', 'success')
                    } else {
                      logEvent('sw-installed', 'Service Worker installed for first time', 'success')
                    }
                    break
                  case 'activating':
                    logEvent('sw-activating', 'Service Worker activating', 'info')
                    break
                  case 'activated':
                    logEvent('sw-activated', 'Service Worker activated', 'success')
                    break
                  case 'redundant':
                    logEvent('sw-error', 'Service Worker became redundant', 'warning')
                    break
                }
              })
            }
          })

          // Check for existing workers
          if (registration.active) {
            logEvent('sw-activated', 'Service Worker is already active', 'success', {
              scriptURL: registration.active.scriptURL
            })
          }

          if (registration.waiting) {
            logEvent('sw-update-available', 'Service Worker update is waiting', 'info')
          }

          if (registration.installing) {
            logEvent('sw-installing', 'Service Worker is installing', 'info')
          }
        })
        .catch(error => {
          logEvent('sw-error', `Service Worker registration failed: ${error.message}`, 'error', {
            error: error.message,
            stack: error.stack
          })
        })

      // Listen for controller changes
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        logEvent('sw-activated', 'New Service Worker took control', 'success')
      })

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', (event) => {
        logEvent('sw-message', 'Message received from Service Worker', 'info', {
          data: event.data
        })
      })
    } else {
      logEvent('sw-error', 'Service Workers not supported in this browser', 'error')
    }

    // PWA Install events
    const handleBeforeInstallPrompt = (e: any) => {
      e.preventDefault() // Prevent automatic prompt
      logEvent('install', 'PWA install prompt available', 'info', {
        platforms: e.platforms,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      })
    }

    const handleAppInstalled = () => {
      logEvent('app-installed', 'PWA installed successfully', 'success', {
        displayMode: window.matchMedia('(display-mode: standalone)').matches ? 'standalone' : 'browser',
        timestamp: new Date().toISOString()
      })
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    // Network events
    const handleOnline = () => {
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
      logEvent('network-online', 'Network connection restored', 'success', {
        effectiveType: connection?.effectiveType,
        downlink: connection?.downlink,
        rtt: connection?.rtt,
        timestamp: new Date().toISOString()
      })
    }

    const handleOffline = () => {
      logEvent('network-offline', 'Network connection lost', 'warning', {
        timestamp: new Date().toISOString()
      })
    }

    const handleConnectionChange = () => {
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
      if (connection) {
        logEvent('network-change', `Network changed to ${connection.effectiveType}`, 'info', {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          saveData: connection.saveData,
          timestamp: new Date().toISOString()
        })
      }
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
    if (connection) {
      connection.addEventListener('change', handleConnectionChange)

      // Log initial connection info
      logEvent('network-info', 'Initial network connection info', 'info', {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData,
        online: navigator.onLine
      })
    }

    // Visibility change events
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden
      logEvent('visibility-change', `Page ${isVisible ? 'visible' : 'hidden'}`, 'info', {
        visible: isVisible,
        visibilityState: document.visibilityState,
        timestamp: new Date().toISOString()
      })
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Page unload event
    const handleBeforeUnload = () => {
      logEvent('page-unload', 'Page unloading', 'info', {
        timestamp: new Date().toISOString()
      })
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    // Additional PWA feature detection
    if ('caches' in window) {
      logEvent('cache-available', 'Cache API is available', 'info')
    }

    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      logEvent('background-sync-available', 'Background Sync API is available', 'info')
    }

    if ('serviceWorker' in navigator && 'PushManager' in window) {
      logEvent('push-available', 'Push API is available', 'info')

      if ('Notification' in window) {
        logEvent('notification-permission', `Notification permission: ${Notification.permission}`, 'info', {
          permission: Notification.permission
        })
      }
    }

    // Log display mode
    const displayModes = ['fullscreen', 'standalone', 'minimal-ui', 'browser']
    const currentDisplayMode = displayModes.find(mode =>
      window.matchMedia(`(display-mode: ${mode})`).matches
    ) || 'browser'

    logEvent('display-mode', `Running in ${currentDisplayMode} mode`, 'info', {
      displayMode: currentDisplayMode,
      isStandalone: window.matchMedia('(display-mode: standalone)').matches
    })

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      if (connection) {
        connection.removeEventListener('change', handleConnectionChange)
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [maxEvents, autoScroll, onEventLogged])

  const clearEvents = () => {
    setEvents([])
    logEvent('page-load', 'Event log cleared', 'info')
  }

  const exportEvents = () => {
    const data = JSON.stringify(events, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `pwa-events-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    logEvent('page-load', 'Events exported to file', 'info')
  }

  const getEventIcon = (type: PWAEventType) => {
    switch (type) {
      case 'install':
      case 'app-installed':
        return Smartphone
      case 'sw-registered':
      case 'sw-installed':
      case 'sw-activated':
        return CheckCircle
      case 'sw-installing':
      case 'sw-activating':
        return Clock
      case 'sw-error':
        return AlertCircle
      case 'sw-update-available':
      case 'sw-update-installed':
        return RefreshCw
      case 'network-online':
        return Wifi
      case 'network-offline':
        return WifiOff
      case 'network-change':
        return Activity
      default:
        return Settings
    }
  }

  const getLevelColor = (level: PWAEvent['level']) => {
    switch (level) {
      case 'success':
        return 'text-green-600'
      case 'warning':
        return 'text-yellow-600'
      case 'error':
        return 'text-red-600'
      default:
        return 'text-blue-600'
    }
  }

  const getLevelBadge = (level: PWAEvent['level']) => {
    switch (level) {
      case 'success':
        return { variant: 'default' as const, className: 'bg-green-500' }
      case 'warning':
        return { variant: 'default' as const, className: 'bg-yellow-500' }
      case 'error':
        return { variant: 'destructive' as const, className: '' }
      default:
        return { variant: 'default' as const, className: 'bg-blue-500' }
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              PWA Event Log
            </CardTitle>
            <CardDescription>
              Real-time log of PWA events and activities ({events.length} events)
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={exportEvents}
              disabled={events.length === 0}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearEvents}
              disabled={events.length === 0}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[400px] w-full overflow-y-auto" ref={scrollAreaRef}>
          {events.length === 0 ? (
            <div className="flex items-center justify-center h-32 text-muted-foreground">
              <p>No events logged yet</p>
            </div>
          ) : (
            <div className="space-y-2">
              {events.map((event, index) => {
                const Icon = getEventIcon(event.type)
                const levelColor = getLevelColor(event.level)
                const levelBadge = getLevelBadge(event.level)
                
                return (
                  <div key={event.id}>
                    <div className="flex items-start gap-3 p-2 rounded-lg hover:bg-muted/50">
                      <Icon className={cn("h-4 w-4 mt-0.5 flex-shrink-0", levelColor)} />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge 
                            variant={levelBadge.variant}
                            className={cn("text-xs", levelBadge.className)}
                          >
                            {event.level}
                          </Badge>
                          {showTimestamps && (
                            <span className="text-xs text-muted-foreground">
                              {event.timestamp.toLocaleTimeString()}
                            </span>
                          )}
                        </div>
                        <p className="text-sm font-medium">{event.message}</p>
                        {event.data && (
                          <pre className="text-xs text-muted-foreground mt-1 bg-muted p-2 rounded overflow-x-auto">
                            {JSON.stringify(event.data, null, 2)}
                          </pre>
                        )}
                      </div>
                    </div>
                    {index < events.length - 1 && <Separator className="my-2" />}
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
