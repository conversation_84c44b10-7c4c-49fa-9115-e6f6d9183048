import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { Button } from "~/components/ui/button"
import { Badge } from "~/components/ui/badge"
// ScrollArea component not available, using div with overflow
import { Separator } from "~/components/ui/separator"
import { 
  Activity, 
  Trash2, 
  Download,
  Wifi,
  WifiOff,
  Smartphone,
  Settings,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react"
import { cn } from "~/lib/utils"

export type PWAEventType = 
  | 'install'
  | 'app-installed'
  | 'sw-registered'
  | 'sw-installing'
  | 'sw-installed'
  | 'sw-activating'
  | 'sw-activated'
  | 'sw-error'
  | 'sw-update-available'
  | 'sw-update-installed'
  | 'network-online'
  | 'network-offline'
  | 'network-change'
  | 'page-load'
  | 'page-unload'
  | 'visibility-change'

export interface PWAEvent {
  id: string
  type: PWAEventType
  timestamp: Date
  message: string
  data?: any
  level: 'info' | 'warning' | 'error' | 'success'
}

export interface PWAEventLoggerProps {
  className?: string
  maxEvents?: number
  autoScroll?: boolean
  showTimestamps?: boolean
  onEventLogged?: (event: PWAEvent) => void
}

export function PWAEventLogger({ 
  className, 
  maxEvents = 100, 
  autoScroll = true,
  showTimestamps = true,
  onEventLogged 
}: PWAEventLoggerProps) {
  const [events, setEvents] = useState<PWAEvent[]>([])
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const eventIdCounter = useRef(0)

  const logEvent = (
    type: PWAEventType, 
    message: string, 
    level: PWAEvent['level'] = 'info',
    data?: any
  ) => {
    const event: PWAEvent = {
      id: `event-${++eventIdCounter.current}`,
      type,
      timestamp: new Date(),
      message,
      level,
      data
    }

    setEvents(prev => {
      const newEvents = [event, ...prev].slice(0, maxEvents)
      return newEvents
    })

    onEventLogged?.(event)

    if (autoScroll && scrollAreaRef.current) {
      setTimeout(() => {
        scrollAreaRef.current?.scrollTo({ top: 0, behavior: 'smooth' })
      }, 100)
    }
  }

  useEffect(() => {
    // Log initial page load
    logEvent('page-load', 'Page loaded', 'info')

    // Service Worker events
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then(registration => {
          logEvent('sw-registered', 'Service Worker registered successfully', 'success', {
            scope: registration.scope
          })

          registration.addEventListener('updatefound', () => {
            logEvent('sw-update-available', 'Service Worker update found', 'info')
            
            const newWorker = registration.installing
            if (newWorker) {
              logEvent('sw-installing', 'New Service Worker installing', 'info')
              
              newWorker.addEventListener('statechange', () => {
                switch (newWorker.state) {
                  case 'installed':
                    if (navigator.serviceWorker.controller) {
                      logEvent('sw-update-installed', 'Service Worker update installed', 'success')
                    } else {
                      logEvent('sw-installed', 'Service Worker installed for first time', 'success')
                    }
                    break
                  case 'activating':
                    logEvent('sw-activating', 'Service Worker activating', 'info')
                    break
                  case 'activated':
                    logEvent('sw-activated', 'Service Worker activated', 'success')
                    break
                  case 'redundant':
                    logEvent('sw-error', 'Service Worker became redundant', 'error')
                    break
                }
              })
            }
          })
        })
        .catch(error => {
          logEvent('sw-error', `Service Worker registration failed: ${error.message}`, 'error', error)
        })

      navigator.serviceWorker.addEventListener('controllerchange', () => {
        logEvent('sw-activated', 'New Service Worker took control', 'success')
      })
    }

    // PWA Install events
    const handleBeforeInstallPrompt = (e: any) => {
      logEvent('install', 'PWA install prompt available', 'info', {
        platforms: e.platforms
      })
    }

    const handleAppInstalled = () => {
      logEvent('app-installed', 'PWA installed successfully', 'success')
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    // Network events
    const handleOnline = () => {
      logEvent('network-online', 'Network connection restored', 'success')
    }

    const handleOffline = () => {
      logEvent('network-offline', 'Network connection lost', 'warning')
    }

    const handleConnectionChange = () => {
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
      if (connection) {
        logEvent('network-change', `Network changed to ${connection.effectiveType}`, 'info', {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt
        })
      }
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
    if (connection) {
      connection.addEventListener('change', handleConnectionChange)
    }

    // Visibility change events
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden
      logEvent('visibility-change', `Page ${isVisible ? 'visible' : 'hidden'}`, 'info', {
        visible: isVisible
      })
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Page unload event
    const handleBeforeUnload = () => {
      logEvent('page-unload', 'Page unloading', 'info')
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      if (connection) {
        connection.removeEventListener('change', handleConnectionChange)
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [maxEvents, autoScroll, onEventLogged])

  const clearEvents = () => {
    setEvents([])
    logEvent('page-load', 'Event log cleared', 'info')
  }

  const exportEvents = () => {
    const data = JSON.stringify(events, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `pwa-events-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    logEvent('page-load', 'Events exported to file', 'info')
  }

  const getEventIcon = (type: PWAEventType) => {
    switch (type) {
      case 'install':
      case 'app-installed':
        return Smartphone
      case 'sw-registered':
      case 'sw-installed':
      case 'sw-activated':
        return CheckCircle
      case 'sw-installing':
      case 'sw-activating':
        return Clock
      case 'sw-error':
        return AlertCircle
      case 'sw-update-available':
      case 'sw-update-installed':
        return RefreshCw
      case 'network-online':
        return Wifi
      case 'network-offline':
        return WifiOff
      case 'network-change':
        return Activity
      default:
        return Settings
    }
  }

  const getLevelColor = (level: PWAEvent['level']) => {
    switch (level) {
      case 'success':
        return 'text-green-600'
      case 'warning':
        return 'text-yellow-600'
      case 'error':
        return 'text-red-600'
      default:
        return 'text-blue-600'
    }
  }

  const getLevelBadge = (level: PWAEvent['level']) => {
    switch (level) {
      case 'success':
        return { variant: 'default' as const, className: 'bg-green-500' }
      case 'warning':
        return { variant: 'default' as const, className: 'bg-yellow-500' }
      case 'error':
        return { variant: 'destructive' as const, className: '' }
      default:
        return { variant: 'default' as const, className: 'bg-blue-500' }
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              PWA Event Log
            </CardTitle>
            <CardDescription>
              Real-time log of PWA events and activities ({events.length} events)
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={exportEvents}
              disabled={events.length === 0}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearEvents}
              disabled={events.length === 0}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[400px] w-full overflow-y-auto" ref={scrollAreaRef}>
          {events.length === 0 ? (
            <div className="flex items-center justify-center h-32 text-muted-foreground">
              <p>No events logged yet</p>
            </div>
          ) : (
            <div className="space-y-2">
              {events.map((event, index) => {
                const Icon = getEventIcon(event.type)
                const levelColor = getLevelColor(event.level)
                const levelBadge = getLevelBadge(event.level)
                
                return (
                  <div key={event.id}>
                    <div className="flex items-start gap-3 p-2 rounded-lg hover:bg-muted/50">
                      <Icon className={cn("h-4 w-4 mt-0.5 flex-shrink-0", levelColor)} />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge 
                            variant={levelBadge.variant}
                            className={cn("text-xs", levelBadge.className)}
                          >
                            {event.level}
                          </Badge>
                          {showTimestamps && (
                            <span className="text-xs text-muted-foreground">
                              {event.timestamp.toLocaleTimeString()}
                            </span>
                          )}
                        </div>
                        <p className="text-sm font-medium">{event.message}</p>
                        {event.data && (
                          <pre className="text-xs text-muted-foreground mt-1 bg-muted p-2 rounded overflow-x-auto">
                            {JSON.stringify(event.data, null, 2)}
                          </pre>
                        )}
                      </div>
                    </div>
                    {index < events.length - 1 && <Separator className="my-2" />}
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
