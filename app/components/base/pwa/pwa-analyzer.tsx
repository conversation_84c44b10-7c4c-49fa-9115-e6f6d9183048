"use client"

import { useEffect, useState, useCallback } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alogT<PERSON>le,
  DialogFooter,
} from "~/components/ui/dialog"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { CheckCircle2, XCircle, AlertCircle, RefreshCw, FileJson } from "lucide-react"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs"
import { Progress } from "~/components/ui/progress"

interface PwaAnalyzerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface CheckResult {
  name: string
  status: "success" | "error" | "warning" | "pending"
  details: string
  fix?: string
}

export default function PwaAnalyzer({ open, onOpenChange }: PwaAnalyzerProps) {
  const [checks, setChecks] = useState<CheckResult[]>([])
  const [manifestContent, setManifestContent] = useState<string | null>(null)
  const [manifestUrl, setManifestUrl] = useState<string | null>(null)
  const [serviceWorkerStatus, setServiceWorkerStatus] = useState<string | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(true)
  const [progress, setProgress] = useState(0)

  const runChecks = useCallback(async () => {
    setIsAnalyzing(true)
    setProgress(0)
    setChecks([])

    const newChecks: CheckResult[] = []

    // Check 1: HTTPS
    setProgress(10)
    const isHttps =
      window.location.protocol === "https:" ||
      window.location.hostname === "localhost" ||
      window.location.hostname === "127.0.0.1"

    newChecks.push({
      name: "HTTPS",
      status: isHttps ? "success" : "error",
      details: isHttps
        ? "Your site is served over HTTPS or localhost, which is required for PWAs."
        : "Your site is not served over HTTPS. PWAs require a secure context.",
      fix: isHttps
        ? undefined
        : "Deploy your site to a hosting provider that supports HTTPS, such as Vercel or Netlify.",
    })

    // Check 2: Service Worker
    setProgress(20)
    let serviceWorkerCheck: CheckResult = {
      name: "Service Worker",
      status: "pending",
      details: "Checking service worker registration...",
    }

    if (!("serviceWorker" in navigator)) {
      serviceWorkerCheck = {
        name: "Service Worker",
        status: "error",
        details: "Service Worker is not supported in this browser.",
        fix: "Use a modern browser that supports Service Workers.",
      }
    } else {
      try {
        const registrations = await navigator.serviceWorker.getRegistrations()
        if (registrations.length === 0) {
          serviceWorkerCheck = {
            name: "Service Worker",
            status: "error",
            details: "No Service Worker is registered for this site.",
            fix: "Ensure your service worker registration code is running correctly.",
          }
        } else {
          const swStatus = registrations
            .map(
              (r) =>
                `Scope: ${r.scope}, State: ${r.active ? "active" : r.installing ? "installing" : r.waiting ? "waiting" : "unknown"}`,
            )
            .join(", ")

          setServiceWorkerStatus(swStatus)

          serviceWorkerCheck = {
            name: "Service Worker",
            status: "success",
            details: `Service Worker is registered. ${swStatus}`,
          }
        }
      } catch (error) {
        serviceWorkerCheck = {
          name: "Service Worker",
          status: "error",
          details: `Error checking Service Worker: ${(error as Error).message}`,
          fix: "Check the console for more details about the service worker error.",
        }
      }
    }

    newChecks.push(serviceWorkerCheck)

    // Check 3: Web App Manifest
    setProgress(40)
    let manifestCheck: CheckResult = {
      name: "Web App Manifest",
      status: "pending",
      details: "Checking manifest...",
    }

    const manifestLinks = document.querySelectorAll('link[rel="manifest"]')
    if (manifestLinks.length === 0) {
      manifestCheck = {
        name: "Web App Manifest",
        status: "error",
        details: "No manifest link found in the document head.",
        fix: 'Add a <link rel="manifest" href="/manifest.json"> tag to your HTML head.',
      }
    } else {
      const manifestHref = manifestLinks[0].getAttribute("href")
      if (!manifestHref) {
        manifestCheck = {
          name: "Web App Manifest",
          status: "error",
          details: "Manifest link has no href attribute.",
          fix: "Ensure your manifest link has a valid href attribute.",
        }
      } else {
        setManifestUrl(manifestHref)
        try {
          const response = await fetch(manifestHref, {
            headers: {
              Accept: "application/json",
              "Cache-Control": "no-cache",
            },
          })

          if (!response.ok) {
            manifestCheck = {
              name: "Web App Manifest",
              status: "error",
              details: `Failed to fetch manifest file: ${response.status} ${response.statusText}`,
              fix: `Check that the file exists at ${manifestHref} and is accessible. Verify the server is correctly serving JSON files with the proper MIME type.`,
            }
          } else {
            try {
              const manifestText = await response.text()
              try {
                // Try to parse the JSON to validate it
                const manifestData = JSON.parse(manifestText)
                setManifestContent(JSON.stringify(manifestData, null, 2))

                const requiredFields = ["name", "short_name", "icons", "start_url", "display"]
                const missingFields = requiredFields.filter((field) => {
                  if (field === "icons") {
                    return !manifestData.icons || !Array.isArray(manifestData.icons) || manifestData.icons.length === 0
                  }
                  return !manifestData[field]
                })

                if (missingFields.length > 0) {
                  manifestCheck = {
                    name: "Web App Manifest",
                    status: "warning",
                    details: `Manifest found but missing recommended fields: ${missingFields.join(", ")}`,
                    fix: `Add the missing fields to your manifest.json file.`,
                  }
                } else {
                  // Check for icons
                  const has192Icon = manifestData.icons?.some(
                    (icon: any) => icon.sizes?.includes("192x192") || icon.sizes === "192x192",
                  )

                  const has512Icon = manifestData.icons?.some(
                    (icon: any) => icon.sizes?.includes("512x512") || icon.sizes === "512x512",
                  )

                  if (!has192Icon || !has512Icon) {
                    manifestCheck = {
                      name: "Web App Manifest",
                      status: "warning",
                      details: `Manifest is missing recommended icon sizes. Found 192x192: ${has192Icon}, Found 512x512: ${has512Icon}`,
                      fix: "Add both 192x192 and 512x512 icons to your manifest.",
                    }
                  } else {
                    manifestCheck = {
                      name: "Web App Manifest",
                      status: "success",
                      details: "Valid manifest found with all required fields.",
                    }
                  }
                }
              } catch (e) {
                // If JSON parsing fails, show the raw text for debugging
                setManifestContent(manifestText)

                manifestCheck = {
                  name: "Web App Manifest",
                  status: "error",
                  details: "Manifest is not valid JSON. Check for syntax errors.",
                  fix: "Validate your JSON using a tool like JSONLint. Common errors include missing commas, extra commas, or unquoted property names.",
                }
              }
            } catch (e) {
              manifestCheck = {
                name: "Web App Manifest",
                status: "error",
                details: `Error reading manifest response: ${(e as Error).message}`,
                fix: "Check your network connection and server configuration.",
              }
            }
          }
        } catch (e) {
          manifestCheck = {
            name: "Web App Manifest",
            status: "error",
            details: `Error fetching manifest: ${(e as Error).message}`,
            fix: "Check your network connection and server configuration.",
          }
        }
      }
    }

    newChecks.push(manifestCheck)

    // Check 4: Installability
    setProgress(60)
    let installabilityCheck: CheckResult = {
      name: "Installability",
      status: "pending",
      details: "Checking if app is installable...",
    }

    // Check if the app is already installed
    const isStandalone =
      window.matchMedia("(display-mode: standalone)").matches || (window.navigator as any).standalone === true

    if (isStandalone) {
      installabilityCheck = {
        name: "Installability",
        status: "success",
        details: "App is already installed and running in standalone mode.",
      }
    } else {
      // Check for beforeinstallprompt support
      if ("BeforeInstallPromptEvent" in window || "onbeforeinstallprompt" in window) {
        installabilityCheck = {
          name: "Installability",
          status: "success",
          details: "Browser supports app installation. The install prompt will appear when browser criteria are met.",
        }
      } else {
        installabilityCheck = {
          name: "Installability",
          status: "warning",
          details: "This browser may not support the installation prompt API.",
          fix: "On iOS, users need to use the 'Add to Home Screen' option in the share menu.",
        }
      }
    }

    newChecks.push(installabilityCheck)

    // Check 5: Icons
    setProgress(80)
    let iconsCheck: CheckResult = {
      name: "Icons",
      status: "pending",
      details: "Checking app icons...",
    }

    const appleIconLink = document.querySelector('link[rel="apple-touch-icon"]')
    if (!appleIconLink) {
      iconsCheck = {
        name: "Icons",
        status: "warning",
        details: "No apple-touch-icon found. This is required for iOS home screen icons.",
        fix: 'Add <link rel="apple-touch-icon" href="/path/to/icon.png"> to your HTML head.',
      }
    } else {
      iconsCheck = {
        name: "Icons",
        status: "success",
        details: "Apple touch icon found.",
      }
    }

    newChecks.push(iconsCheck)

    // Check 6: Theme Color
    setProgress(90)
    let themeColorCheck: CheckResult = {
      name: "Theme Color",
      status: "pending",
      details: "Checking theme color...",
    }

    const themeColorMeta = document.querySelector('meta[name="theme-color"]')
    if (!themeColorMeta) {
      themeColorCheck = {
        name: "Theme Color",
        status: "warning",
        details: "No theme-color meta tag found. This is used for the browser UI color.",
        fix: 'Add <meta name="theme-color" content="#colorcode"> to your HTML head.',
      }
    } else {
      themeColorCheck = {
        name: "Theme Color",
        status: "success",
        details: `Theme color set to ${themeColorMeta.getAttribute("content")}.`,
      }
    }

    newChecks.push(themeColorCheck)

    // Final check: Overall installability
    setProgress(100)
    const criticalErrors = newChecks.filter(
      (check) =>
        check.status === "error" &&
        (check.name === "HTTPS" || check.name === "Service Worker" || check.name === "Web App Manifest"),
    )

    if (criticalErrors.length === 0) {
      newChecks.push({
        name: "Overall Status",
        status: "success",
        details: "Your PWA meets the basic requirements for installation.",
      })
    } else {
      newChecks.push({
        name: "Overall Status",
        status: "error",
        details: `Your PWA has ${criticalErrors.length} critical issues that prevent installation.`,
        fix: "Fix the critical issues listed above to make your PWA installable.",
      })
    }

    setChecks(newChecks)
    setIsAnalyzing(false)
  }, []) // Empty dependency array since runChecks doesn't depend on props/state

  useEffect(() => {
    if (open) {
      runChecks()
    }
  }, [open, runChecks])

  const fixManifest = async () => {
    try {
      // Create a corrected manifest.json
      const correctedManifest = {
        name: "Animated Menu PWA",
        short_name: "Menu PWA",
        description: "A Progressive Web App with animated menu and offline capabilities",
        start_url: "/",
        display: "standalone",
        orientation: "portrait",
        background_color: "#ffffff",
        theme_color: "#4f46e5",
        dir: "ltr",
        lang: "en-US",
        scope: "/",
        categories: ["productivity", "utilities"],
        prefer_related_applications: false,
        icons: [
          {
            src: "/icons/icon-192x192.png",
            sizes: "192x192",
            type: "image/png",
          },
          {
            src: "/icons/icon-512x512.png",
            sizes: "512x512",
            type: "image/png",
          },
          {
            src: "/icons/maskable-icon.png",
            sizes: "512x512",
            type: "image/png",
            purpose: "maskable",
          },
        ],
        shortcuts: [
          {
            name: "Notes",
            short_name: "Notes",
            description: "View your offline notes",
            url: "/notes",
            icons: [{ src: "/icons/icon-192x192.png", sizes: "192x192" }],
          },
          {
            name: "Offline Page",
            short_name: "Offline",
            description: "View offline page",
            url: "/offline",
            icons: [{ src: "/icons/icon-192x192.png", sizes: "192x192" }],
          },
        ],
      }

      setManifestContent(JSON.stringify(correctedManifest, null, 2))

      // Create a Blob with the corrected manifest
      const blob = new Blob([JSON.stringify(correctedManifest, null, 2)], {
        type: "application/json",
      })

      // Create a download link
      const a = document.createElement("a")
      a.download = "manifest.json"
      a.href = URL.createObjectURL(blob)
      a.textContent = "Download corrected manifest.json"

      // Trigger download
      a.click()

      // Clean up
      URL.revokeObjectURL(a.href)

      // Show success message
      alert(
        "A corrected manifest.json file has been downloaded. Please replace your current manifest.json with this file.",
      )

      // Don't automatically re-run checks to prevent loops
      // User can manually click "Run Checks Again" if needed
    } catch (error) {
      console.error("Error creating manifest file:", error)
      alert("Failed to create manifest file. See console for details.")
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>PWA Installation Diagnostics</DialogTitle>
          <DialogDescription>Detailed analysis of your Progressive Web App installation requirements</DialogDescription>
        </DialogHeader>

        {isAnalyzing ? (
          <div className="py-8 space-y-4">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 mx-auto animate-spin text-primary" />
              <p className="mt-4">Analyzing PWA requirements...</p>
            </div>
            <Progress value={progress} className="w-full" />
          </div>
        ) : (
          <Tabs defaultValue="summary">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="summary">Summary</TabsTrigger>
              <TabsTrigger value="manifest">Manifest</TabsTrigger>
              <TabsTrigger value="serviceworker">Service Worker</TabsTrigger>
            </TabsList>

            <TabsContent value="summary" className="space-y-4 pt-4">
              <div className="space-y-4">
                {checks.map((check, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      {check.status === "success" && (
                        <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      )}
                      {check.status === "error" && <XCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />}
                      {check.status === "warning" && (
                        <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
                      )}
                      <div className="space-y-1 flex-1">
                        <h3 className="font-medium">{check.name}</h3>
                        <p className="text-sm text-muted-foreground">{check.details}</p>
                        {check.fix && (
                          <div className="mt-2 text-sm bg-muted p-2 rounded-md">
                            <span className="font-medium">How to fix:</span> {check.fix}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="manifest" className="pt-4">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Web App Manifest</h3>
                  <div className="flex gap-2">
                    {manifestUrl && (
                      <Button variant="outline" size="sm" onClick={() => window.open(manifestUrl, "_blank")}>
                        View Raw
                      </Button>
                    )}
                    <Button variant="outline" size="sm" onClick={fixManifest} className="flex items-center gap-1">
                      <FileJson className="h-4 w-4" />
                      <span>Download Fixed Manifest</span>
                    </Button>
                  </div>
                </div>

                {manifestContent ? (
                  <pre className="bg-muted p-4 rounded-md overflow-x-auto text-xs whitespace-pre-wrap">
                    {manifestContent}
                  </pre>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">No manifest content available</div>
                )}

                <div className="bg-muted p-4 rounded-md">
                  <h4 className="font-medium mb-2">Required Manifest Fields</h4>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    <li>
                      <strong>name</strong>: The full name of your application
                    </li>
                    <li>
                      <strong>short_name</strong>: A short name for use on home screens
                    </li>
                    <li>
                      <strong>icons</strong>: At least 192x192 and 512x512 icons
                    </li>
                    <li>
                      <strong>start_url</strong>: The URL that loads when app is launched
                    </li>
                    <li>
                      <strong>display</strong>: How the app should be displayed (standalone recommended)
                    </li>
                  </ul>
                </div>

                <div className="bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800 p-4 rounded-md">
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-amber-500" />
                    <span>Common Manifest Issues</span>
                  </h4>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    <li>JSON syntax errors (missing commas, extra commas, unquoted property names)</li>
                    <li>Incorrect MIME type (server should serve as application/json)</li>
                    <li>Incorrect file path or permissions</li>
                    <li>Cross-origin issues (manifest must be same-origin or have proper CORS headers)</li>
                  </ul>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="serviceworker" className="pt-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Service Worker Status</h3>

                {serviceWorkerStatus ? (
                  <div className="bg-muted p-4 rounded-md">
                    <p className="text-sm">{serviceWorkerStatus}</p>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">No service worker information available</div>
                )}

                <div className="bg-muted p-4 rounded-md">
                  <h4 className="font-medium mb-2">Service Worker Requirements</h4>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    <li>Must be registered and active</li>
                    <li>Should implement caching strategies for offline support</li>
                    <li>Should handle fetch events for network requests</li>
                    <li>Should have a strategy for updates</li>
                  </ul>
                </div>

                <Button
                  onClick={async () => {
                    if ("serviceWorker" in navigator) {
                      try {
                        const registrations = await navigator.serviceWorker.getRegistrations()
                        for (const registration of registrations) {
                          await registration.unregister()
                        }
                        window.location.reload()
                      } catch (error) {
                        console.error("Service worker unregistration failed:", error)
                      }
                    }
                  }}
                  variant="outline"
                  className="w-full"
                >
                  Unregister Service Workers & Reload
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        )}

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={() => runChecks()} disabled={isAnalyzing} className="gap-2">
            <RefreshCw className={`h-4 w-4 ${isAnalyzing ? "animate-spin" : ""}`} />
            Run Checks Again
          </Button>
          <Button onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
