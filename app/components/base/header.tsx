import * as React from "react"
import {
  B<PERSON><PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "~/components/ui/breadcrumb"
import { Separator } from "~/components/ui/separator"
import { SidebarTrigger } from "~/components/ui/sidebar"

export interface BreadcrumbItem {
  title: string
  href?: string
  isCurrentPage?: boolean
}

export interface DashboardHeaderProps {
  title: string
  breadcrumbItems?: BreadcrumbItem[]
  actions?: React.ReactNode
  rightActions?: React.ReactNode
}

export function DashboardHeader({
  title,
  breadcrumbItems = [],
  actions,
  rightActions
}: DashboardHeaderProps) {
  return (
    <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-2 px-4 flex-1">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mr-2 data-[orientation=vertical]:h-4"
        />XXXXX
        <Breadcrumb>
          <BreadcrumbList>
            {breadcrumbItems.map((item, index) => (
              <React.Fragment key={index}>
                {index > 0 && <BreadcrumbSeparator className="hidden md:block" />}
                <BreadcrumbItem className={index === 0 ? "hidden md:block" : ""}>
                  {item.isCurrentPage ? (
                    <BreadcrumbPage>{item.title}</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink href={item.href || "#"}>
                      {item.title}
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
              </React.Fragment>
            ))}
            {breadcrumbItems.length === 0 && (
              <BreadcrumbItem>
                <BreadcrumbPage>{title}</BreadcrumbPage>
              </BreadcrumbItem>
            )}
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* Center actions (legacy support) */}
      {actions && (
        <div className="px-2">
          {actions}
        </div>
      )}

      {/* Right-aligned actions */}
      {rightActions && (
        <div className="flex items-center gap-2 px-4">
          {rightActions}
        </div>
      )}
    </header>
  )
}
