import React from "react"
import { HeaderIcon } from "./icons/header-icons"
import { HeaderIconDropdown } from "./icons/header-icons-dropdown"
import { NotificationBadge } from "./badge"
import { DropdownContent } from "./dropdown-content"
import type { HeaderAction } from "~/services/header-notifications"
import type { BadgeColor } from "./badge"

export interface HeaderActionsProps {
  actions: HeaderAction[]
  className?: string
}

export function HeaderActions({ actions, className }: HeaderActionsProps) {
  return (
    <div className={`flex items-center gap-2 ${className || ''}`}>
      {actions.map((action) => (
        <HeaderActionItem key={action.id} action={action} />
      ))}
    </div>
  )
}

interface HeaderActionItemProps {
  action: HeaderAction
}

function HeaderActionItem({ action }: HeaderActionItemProps) {
  const { type, icon, label, onClick, badge, dropdown } = action

  // Render simple icon button
  if (type === 'icon') {
    return (
      <div className="relative">
        <HeaderIcon
          icon={icon}
          onClick={onClick}
          aria-label={label}
        />
        {badge && renderBadge(badge)}
      </div>
    )
  }

  // Render icon with dropdown
  if (type === 'dropdown' && dropdown) {
    return (
      <div className="relative">
        <HeaderIconDropdown
          icon={icon}
          dropdownContent={<DropdownContent data={dropdown.data} />}
          position={dropdown.position}
          aria-label={label}
        />
        {badge && renderBadge(badge)}
      </div>
    )
  }

  // Render icon with badge (legacy support)
  if (type === 'badge') {
    return (
      <div className="relative">
        <HeaderIcon
          icon={icon}
          onClick={onClick}
          aria-label={label}
        />
        {badge && renderBadge(badge)}
      </div>
    )
  }

  return null
}

function renderBadge(badge: NonNullable<HeaderAction['badge']>) {
  const { count, text, color = 'red', blink = false, blinkInterval = 1000 } = badge
  
  const badgeText = text || (count !== undefined ? count.toString() : '')
  
  if (!badgeText || (count !== undefined && count <= 0)) {
    return null
  }

  return (
    <div className="absolute -top-1 -right-1 pointer-events-none">
      <NotificationBadge
        text={badgeText}
        color={color as BadgeColor}
        shape="circular"
        blinkInterval={blink ? blinkInterval : 0}
      />
    </div>
  )
}
