import { useState, useEffect } from "react"
import { cn } from "~/lib/utils"

// Color enum for predefined colors
export type BadgeColor = 
  | "red" 
  | "blue" 
  | "green" 
  | "yellow" 
  | "purple" 
  | "gray" 
  | "orange"
  | string

// Shape variants
export type BadgeShape = "circular" | "triangle" | "square"

export interface NotificationBadgeProps {
  text: string
  color?: BadgeColor
  shape?: BadgeShape
  blinkInterval?: number
  className?: string
}

// Custom hook for blinking effect
function useBlink(interval: number = 0) {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    if (interval <= 0) return

    const timer = setInterval(() => {
      setIsVisible(prev => !prev)
    }, interval)

    return () => clearInterval(timer)
  }, [interval])

  return isVisible
}

// Color mapping for predefined colors
const colorClasses: Record<string, string> = {
  red: "bg-red-500 text-white",
  blue: "bg-blue-500 text-white", 
  green: "bg-green-500 text-white",
  yellow: "bg-yellow-500 text-black",
  purple: "bg-purple-500 text-white",
  gray: "bg-gray-500 text-white",
  orange: "bg-orange-500 text-white"
}

// Shape mapping
const shapeClasses: Record<BadgeShape, string> = {
  circular: "rounded-full",
  triangle: "rounded-none", // Will use custom triangle shape
  square: "rounded-sm"
}

export function NotificationBadge({ 
  text, 
  color = "red", 
  shape = "circular", 
  blinkInterval = 0,
  className 
}: NotificationBadgeProps) {
  const isVisible = useBlink(blinkInterval)
  
  // Get color classes - use predefined or custom color
  const colorClass = colorClasses[color] || `bg-${color}-500 text-white`
  
  // Base classes for the badge
  const baseClasses = cn(
    "inline-flex items-center justify-center",
    "text-xs font-bold",
    "min-w-[1.25rem] h-5 px-1",
    "transition-all duration-200",
    colorClass,
    shapeClasses[shape],
    {
      "opacity-0": blinkInterval > 0 && !isVisible,
      "opacity-100": blinkInterval === 0 || isVisible
    },
    className
  )

  // Triangle shape requires special handling
  if (shape === "triangle") {
    return (
      <div className="relative">
        <div 
          className={cn(
            "w-0 h-0 border-l-[10px] border-r-[10px] border-b-[16px]",
            "border-l-transparent border-r-transparent",
            `border-b-${color === "red" ? "red" : color}-500`,
            {
              "opacity-0": blinkInterval > 0 && !isVisible,
              "opacity-100": blinkInterval === 0 || isVisible
            },
            className
          )}
        />
        <span 
          className={cn(
            "absolute top-2 left-1/2 transform -translate-x-1/2",
            "text-xs font-bold text-white",
            {
              "opacity-0": blinkInterval > 0 && !isVisible,
              "opacity-100": blinkInterval === 0 || isVisible
            }
          )}
        >
          {text}
        </span>
      </div>
    )
  }

  return (
    <span className={baseClasses}>
      {text}
    </span>
  )
}

// Alternative implementation using CSS animations
export function NotificationBadgeCSS({ 
  text, 
  color = "red", 
  shape = "circular", 
  blinkInterval = 0,
  className 
}: NotificationBadgeProps) {
  const colorClass = colorClasses[color] || `bg-${color}-500 text-white`
  
  const baseClasses = cn(
    "inline-flex items-center justify-center",
    "text-xs font-bold",
    "min-w-[1.25rem] h-5 px-1",
    colorClass,
    shapeClasses[shape],
    {
      "animate-pulse": blinkInterval > 0 && blinkInterval <= 1000,
      "animate-ping": blinkInterval > 1000
    },
    className
  )

  if (shape === "triangle") {
    return (
      <div className="relative">
        <div 
          className={cn(
            "w-0 h-0 border-l-[10px] border-r-[10px] border-b-[16px]",
            "border-l-transparent border-r-transparent",
            `border-b-${color === "red" ? "red" : color}-500`,
            {
              "animate-pulse": blinkInterval > 0 && blinkInterval <= 1000,
              "animate-ping": blinkInterval > 1000
            },
            className
          )}
        />
        <span className="absolute top-2 left-1/2 transform -translate-x-1/2 text-xs font-bold text-white">
          {text}
        </span>
      </div>
    )
  }

  return (
    <span className={baseClasses}>
      {text}
    </span>
  )
}
