import { useState, useRef, useEffect } from "react"
import type { LucideIcon } from "lucide-react"
import { cn } from "~/lib/utils"
import { HeaderIcon, HeaderIconProps } from "./header-icons"

export interface HeaderIconDropdownProps extends Omit<HeaderIconProps, "onClick"> {
  dropdownContent: React.ReactNode
  position?: "left" | "right"
  onOpenChange?: (open: boolean) => void
  disabled?: boolean
}

export function HeaderIconDropdown({
  icon,
  size = 20,
  className,
  dropdownContent,
  position = "right",
  onOpenChange,
  disabled = false,
  ...props
}: HeaderIconDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
      return () => document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen])

  // Handle escape key
  useEffect(() => {
    function handleEscape(event: KeyboardEvent) {
      if (event.key === "Escape" && isOpen) {
        setIsOpen(false)
        buttonRef.current?.focus()
      }
    }

    if (isOpen) {
      document.addEventListener("keydown", handleEscape)
      return () => document.removeEventListener("keydown", handleEscape)
    }
  }, [isOpen])

  const handleToggle = () => {
    if (disabled) return
    const newOpen = !isOpen
    setIsOpen(newOpen)
    onOpenChange?.(newOpen)
  }

  return (
    <div className="relative">
      <HeaderIcon
        ref={buttonRef}
        icon={icon}
        size={size}
        className={cn(
          {
            "bg-accent text-accent-foreground": isOpen
          },
          className
        )}
        onClick={handleToggle}
        disabled={disabled}
        aria-expanded={isOpen}
        aria-haspopup="true"
        {...props}
      />
      
      {isOpen && (
        <div
          ref={dropdownRef}
          className={cn(
            "absolute top-full mt-2 z-50",
            "min-w-[200px] max-w-[300px]",
            "bg-popover text-popover-foreground",
            "border border-border rounded-md shadow-lg",
            "animate-in fade-in-0 zoom-in-95",
            "data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95",
            {
              "right-0": position === "right",
              "left-0": position === "left"
            }
          )}
          role="menu"
          aria-orientation="vertical"
        >
          {dropdownContent}
        </div>
      )}
    </div>
  )
}

// Dropdown content wrapper components for better structure
export function DropdownHeader({ children, className }: { children: React.ReactNode, className?: string }) {
  return (
    <div className={cn("px-3 py-2 border-b border-border", className)}>
      {children}
    </div>
  )
}

export function DropdownContent({ children, className }: { children: React.ReactNode, className?: string }) {
  return (
    <div className={cn("p-1", className)}>
      {children}
    </div>
  )
}

export function DropdownItem({ 
  children, 
  onClick, 
  className,
  disabled = false 
}: { 
  children: React.ReactNode
  onClick?: () => void
  className?: string
  disabled?: boolean
}) {
  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "w-full text-left px-3 py-2 text-sm",
        "hover:bg-accent hover:text-accent-foreground",
        "focus:bg-accent focus:text-accent-foreground",
        "focus:outline-none",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        "transition-colors duration-200",
        className
      )}
      role="menuitem"
    >
      {children}
    </button>
  )
}

export function DropdownSeparator({ className }: { className?: string }) {
  return <div className={cn("h-px bg-border my-1", className)} />
}
