import { forwardRef } from "react"
import type { LucideIcon } from "lucide-react"
import { cn } from "~/lib/utils"

export interface HeaderIconProps {
  icon: LucideIcon
  size?: number
  className?: string
  onClick?: () => void
  disabled?: boolean
  "aria-label"?: string
}

export const HeaderIcon = forwardRef<HTMLButtonElement, HeaderIconProps>(
  ({ icon: Icon, size = 20, className, onClick, disabled = false, ...props }, ref) => {
    return (
      <button
        ref={ref}
        type="button"
        onClick={onClick}
        disabled={disabled}
        className={cn(
          "inline-flex items-center justify-center",
          "h-9 w-9 rounded-md",
          "text-muted-foreground hover:text-foreground",
          "hover:bg-accent hover:text-accent-foreground",
          "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          "transition-colors duration-200",
          "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent",
          className
        )}
        {...props}
      >
        <Icon size={size} />
      </button>
    )
  }
)

HeaderIcon.displayName = "HeaderIcon"

// Variant for icon without button wrapper (for use in dropdowns, etc.)
export interface HeaderIconRawProps {
  icon: LucideIcon
  size?: number
  className?: string
}

export function HeaderIconRaw({ icon: Icon, size = 20, className }: HeaderIconRawProps) {
  return (
    <Icon 
      size={size} 
      className={cn("text-muted-foreground", className)} 
    />
  )
}
