import * as React from "react"

import { NavMain } from "~/components/base/nav-main"
import { NavProjects } from "~/components/base/nav-projects"
import { NavUser } from "~/components/base/nav-user"
import { TeamSwitcher } from "~/components/base/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "~/components/ui/sidebar"
import { iconMap, type IconName } from "~/lib/icon-map"

// Import JSON data
import sidebarNavigationData from "~/api/sidebar-navigation.json"
import teamsData from "~/api/teams.json"
import projectsData from "~/api/projects.json"
import userProfileData from "~/api/user-profile.json"

// Process JSON data and map icons
const data = {
  user: userProfileData,
  teams: teamsData.map(team => ({
    ...team,
    logo: iconMap[team.logo as IconName]
  })),
  navMain: sidebarNavigationData.map(nav => ({
    ...nav,
    icon: iconMap[nav.icon as IconName]
  })),
  projects: projectsData.map(project => ({
    ...project,
    icon: iconMap[project.icon as IconName]
  }))
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
