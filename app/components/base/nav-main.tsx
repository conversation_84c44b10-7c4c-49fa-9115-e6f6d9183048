"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"
import { useLocation } from "@react-router/dom"
import { NotificationBadge } from "./badge"
import type { BadgeColor } from "./badge"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "~/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "~/components/ui/sidebar"

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
    badge?: {
      count?: number
      text?: string
      color?: string
      blink?: boolean
    }
    items?: {
      title: string
      url: string
    }[]
  }[]
}) {
  const location = useLocation()
  return (
    <SidebarGroup>
      <SidebarGroupLabel>Platform</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          // If item has no children, render as a simple link
          if (!item.items || item.items.length === 0) {
            return (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton asChild tooltip={item.title}>
                  <a href={item.url} className="relative">
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                    {item.badge && (
                      <div className="ml-auto">
                        <NotificationBadge
                          text={item.badge.text || (item.badge.count ? item.badge.count.toString() : '')}
                          color={(item.badge.color as BadgeColor) || 'red'}
                          shape="circular"
                          blinkInterval={item.badge.blink ? 1000 : 0}
                        />
                      </div>
                    )}
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>
            )
          }

          // If item has children, render as collapsible
          // Check if current path matches any of the sub-items
          const isSubItemActive = item.items?.some(subItem => location.pathname === subItem.url) || false
          const shouldBeOpen = item.isActive || isSubItemActive

          return (
            <Collapsible
              key={item.title}
              asChild
              defaultOpen={shouldBeOpen}
              className="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton tooltip={item.title} className="relative">
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                    {item.badge && (
                      <div className="mr-2">
                        <NotificationBadge
                          text={item.badge.text || (item.badge.count ? item.badge.count.toString() : '')}
                          color={(item.badge.color as BadgeColor) || 'red'}
                          shape="circular"
                          blinkInterval={item.badge.blink ? 1000 : 0}
                        />
                      </div>
                    )}
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {item.items?.map((subItem) => {
                      const isCurrentPage = location.pathname === subItem.url
                      return (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton asChild isActive={isCurrentPage}>
                            <a href={subItem.url}>
                              <span>{subItem.title}</span>
                            </a>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      )
                    })}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          )
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
