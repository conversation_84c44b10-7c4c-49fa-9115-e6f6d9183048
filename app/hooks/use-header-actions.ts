import { useState, useEffect, useCallback } from "react"
import type { HeaderAction, NotificationItem, MessageItem } from "~/services/header-notifications"
import { 
  fetchNotifications, 
  fetchMessages, 
  createDefaultHeaderActions 
} from "~/services/header-notifications"

export interface UseHeaderActionsOptions {
  enableNotifications?: boolean
  enableMessages?: boolean
  refreshInterval?: number
  customActions?: HeaderAction[]
  callbacks?: {
    onSearch?: () => void
    onNotificationsView?: () => void
    onMessagesView?: () => void
    onSettings?: () => void
    onProfile?: () => void
  }
}

export interface UseHeaderActionsReturn {
  actions: HeaderAction[]
  notifications: NotificationItem[]
  messages: MessageItem[]
  isLoading: boolean
  error: string | null
  refresh: () => Promise<void>
}

export function useHeaderActions(options: UseHeaderActionsOptions = {}): UseHeaderActionsReturn {
  const {
    enableNotifications = true,
    enableMessages = true,
    refreshInterval = 30000, // 30 seconds
    customActions = [],
    callbacks = {}
  } = options

  const [notifications, setNotifications] = useState<NotificationItem[]>([])
  const [messages, setMessages] = useState<MessageItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setError(null)
      
      const promises: Promise<any>[] = []
      
      if (enableNotifications) {
        promises.push(fetchNotifications())
      }
      
      if (enableMessages) {
        promises.push(fetchMessages())
      }

      if (promises.length === 0) {
        setIsLoading(false)
        return
      }

      const results = await Promise.all(promises)
      
      let notificationResults: NotificationItem[] = []
      let messageResults: MessageItem[] = []
      
      if (enableNotifications && enableMessages) {
        [notificationResults, messageResults] = results
      } else if (enableNotifications) {
        [notificationResults] = results
      } else if (enableMessages) {
        [messageResults] = results
      }
      
      setNotifications(notificationResults)
      setMessages(messageResults)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch header data')
      console.error('Error fetching header data:', err)
    } finally {
      setIsLoading(false)
    }
  }, [enableNotifications, enableMessages])

  // Initial fetch
  useEffect(() => {
    fetchData()
  }, [fetchData])

  // Set up refresh interval
  useEffect(() => {
    if (refreshInterval <= 0) return

    const interval = setInterval(fetchData, refreshInterval)
    return () => clearInterval(interval)
  }, [fetchData, refreshInterval])

  // Generate actions
  const actions = useState(() => {
    if (customActions.length > 0) {
      return customActions
    }
    
    return createDefaultHeaderActions(notifications, messages, callbacks)
  })[0]

  // Update actions when data changes
  useEffect(() => {
    if (customActions.length === 0) {
      // Only update if using default actions
      const newActions = createDefaultHeaderActions(notifications, messages, callbacks)
      // Update the actions state - this is a simplified approach
      // In a real app, you might want to use a reducer or more sophisticated state management
    }
  }, [notifications, messages, customActions, callbacks])

  return {
    actions: customActions.length > 0 ? customActions : createDefaultHeaderActions(notifications, messages, callbacks),
    notifications,
    messages,
    isLoading,
    error,
    refresh: fetchData
  }
}
