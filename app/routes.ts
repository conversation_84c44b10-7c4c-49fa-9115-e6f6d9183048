import { type RouteConfig, index, route } from "@react-router/dev/routes";
import { generateRouteConfig } from "~/config/navigation";

// Generate routes automatically from navigation configuration
const generatedRoutes = generateRouteConfig();

// Convert generated routes to React Router format
const routeEntries = generatedRoutes.map(routeItem => {
  if (routeItem.type === "index") {
    return index(routeItem.file);
  } else {
    return route(routeItem.path, routeItem.file);
  }
});

export default routeEntries satisfies RouteConfig;