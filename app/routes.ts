import { type RouteConfig, index, route } from "@react-router/dev/routes";

export default [
  index("pages/dashboard/home.tsx"),
  route("/dashboard", "pages/dashboard/dashboard.tsx"),
  route("/analytics", "pages/dashboard/analytics.tsx"),
  route("/reports", "pages/dashboard/reports.tsx"),
  route("/users", "pages/admin/users.tsx"),
  route("/settings", "pages/admin/settings.tsx"),
  route("/notifications", "pages/admin/notifications.tsx"),

  route("/pwa", "components/base/pwa/pwa-page.tsx"),
  route("/pwa-demo", "components/base/pwa/pwa-demo-page.tsx"),




  route("*", "pages/404.tsx"), // Catch-all route for 404
] satisfies RouteConfig;