// API Response Types
export interface ApiResponse<T> {
  data: T
  status: number
  message: string
}

// Dashboard Stats Types
export interface StatItem {
  value: number
  change: number
  period: string
}

export interface DashboardStats {
  totalRevenue: StatItem
  subscriptions: StatItem
  sales: StatItem
  activeNow: StatItem
}

// Sales Types
export interface Sale {
  id: string
  customer: string
  email: string
  amount: number
  avatar: string
}

// Orders Types
export interface Order {
  id: string
  customer: string
  email: string
  status: "Fulfilled" | "Pending" | "Cancelled"
  date: string
  amount: number
}

// Products Types
export interface Product {
  id: string
  name: string
  sales: number
  revenue: number
  growth: number
}

// Chart Types
export interface ChartDataPoint {
  name: string
  total: number
}

// API Error Type
export interface ApiError {
  message: string
  status: number
}
