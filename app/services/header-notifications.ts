import type { LucideIcon } from "lucide-react"
import { Bell, MessageSquare, Mail, AlertCircle, Search, Settings, User } from "lucide-react"

export interface NotificationItem {
  id: string
  title: string
  message: string
  timestamp: string
  read?: boolean
  type?: 'info' | 'warning' | 'error' | 'success'
}

export interface MessageItem {
  id: string
  from: string
  subject: string
  preview: string
  timestamp: string
  read?: boolean
  important?: boolean
}

export interface HeaderAction {
  id: string
  type: 'icon' | 'dropdown' | 'badge'
  icon: LucideIcon
  label: string
  onClick?: () => void
  badge?: {
    count?: number
    text?: string
    color?: 'red' | 'blue' | 'green' | 'yellow' | 'purple' | 'gray' | 'orange'
    blink?: boolean
    blinkInterval?: number
  }
  dropdown?: {
    data: DropdownContentData
    position?: 'left' | 'right'
  }
}

// Mock data fetching functions - in real app these would call APIs
export async function fetchNotifications(): Promise<NotificationItem[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100))
  
  return [
    {
      id: 'notif-1',
      title: 'New order received',
      message: 'Order #12345 has been placed',
      timestamp: '2 minutes ago',
      read: false,
      type: 'info'
    },
    {
      id: 'notif-2', 
      title: 'Payment processed',
      message: 'Payment of $299.99 completed',
      timestamp: '5 minutes ago',
      read: false,
      type: 'success'
    },
    {
      id: 'notif-3',
      title: 'Low stock alert',
      message: 'Product inventory is running low',
      timestamp: '10 minutes ago',
      read: true,
      type: 'warning'
    }
  ]
}

export async function fetchMessages(): Promise<MessageItem[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100))
  
  return [
    {
      id: 'msg-1',
      from: 'John Doe',
      subject: 'Project Update',
      preview: 'Hey, how\'s the project going?',
      timestamp: '1 hour ago',
      read: false,
      important: true
    },
    {
      id: 'msg-2',
      from: 'Jane Smith', 
      subject: 'Meeting Request',
      preview: 'Can we schedule a meeting?',
      timestamp: '2 hours ago',
      read: false,
      important: false
    }
  ]
}

export interface DropdownContentData {
  header: {
    title: string
    subtitle: string
  }
  items: Array<{
    id: string
    title: string
    subtitle: string
    onClick?: () => void
  }>
  footer?: {
    text: string
    onClick?: () => void
  }
}

export function createNotificationDropdownData(
  notifications: NotificationItem[],
  onViewAll?: () => void
): DropdownContentData {
  const unreadCount = notifications.filter(n => !n.read).length

  return {
    header: {
      title: 'Notifications',
      subtitle: `You have ${unreadCount} unread notifications`
    },
    items: notifications.slice(0, 3).map((notification) => ({
      id: notification.id,
      title: notification.title,
      subtitle: notification.timestamp,
      onClick: () => console.log('Notification clicked:', notification.id)
    })),
    footer: {
      text: 'View all notifications',
      onClick: onViewAll
    }
  }
}

export function createMessageDropdownData(
  messages: MessageItem[],
  onViewAll?: () => void
): DropdownContentData {
  const unreadCount = messages.filter(m => !m.read).length

  return {
    header: {
      title: 'Messages',
      subtitle: `${unreadCount} new messages`
    },
    items: messages.slice(0, 3).map((message) => ({
      id: message.id,
      title: message.from,
      subtitle: message.preview,
      onClick: () => console.log('Message clicked:', message.id)
    })),
    footer: {
      text: 'View all messages',
      onClick: onViewAll
    }
  }
}

// Default header actions configuration
export function createDefaultHeaderActions(
  notifications: NotificationItem[],
  messages: MessageItem[],
  callbacks?: {
    onSearch?: () => void
    onNotificationsView?: () => void
    onMessagesView?: () => void
    onSettings?: () => void
    onProfile?: () => void
  }
): HeaderAction[] {
  const unreadNotifications = notifications.filter(n => !n.read).length
  const unreadMessages = messages.filter(m => !m.read).length
  
  return [
    {
      id: 'search',
      type: 'icon',
      icon: Search,
      label: 'Search',
      onClick: callbacks?.onSearch
    },
    {
      id: 'notifications',
      type: 'dropdown',
      icon: Bell,
      label: 'Notifications',
      badge: unreadNotifications > 0 ? {
        count: unreadNotifications,
        color: 'red',
        blink: true,
        blinkInterval: 2000
      } : undefined,
      dropdown: {
        data: createNotificationDropdownData(notifications, callbacks?.onNotificationsView),
        position: 'right'
      }
    },
    {
      id: 'messages',
      type: 'dropdown', 
      icon: MessageSquare,
      label: 'Messages',
      badge: unreadMessages > 0 ? {
        count: unreadMessages,
        color: 'blue'
      } : undefined,
      dropdown: {
        data: createMessageDropdownData(messages, callbacks?.onMessagesView),
        position: 'right'
      }
    },
    {
      id: 'mail',
      type: 'icon',
      icon: Mail,
      label: 'Mail',
      onClick: () => console.log('Mail clicked')
    },
    {
      id: 'settings',
      type: 'icon',
      icon: Settings,
      label: 'Settings',
      onClick: callbacks?.onSettings
    },
    {
      id: 'profile',
      type: 'icon',
      icon: User,
      label: 'User Profile',
      onClick: callbacks?.onProfile
    }
  ]
}
