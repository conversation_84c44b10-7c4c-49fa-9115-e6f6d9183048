import type { LucideIcon } from "lucide-react"
import { Bell, MessageSquare, Mail, AlertCircle, Search, Settings, User } from "lucide-react"

export interface NotificationItem {
  id: string
  title: string
  message: string
  timestamp: string
  read?: boolean
  type?: 'info' | 'warning' | 'error' | 'success'
}

export interface MessageItem {
  id: string
  from: string
  subject: string
  preview: string
  timestamp: string
  read?: boolean
  important?: boolean
}

export interface HeaderAction {
  id: string
  type: 'icon' | 'dropdown' | 'badge'
  icon: LucideIcon
  label: string
  onClick?: () => void
  badge?: {
    count?: number
    text?: string
    color?: 'red' | 'blue' | 'green' | 'yellow' | 'purple' | 'gray' | 'orange'
    blink?: boolean
    blinkInterval?: number
  }
  dropdown?: {
    content: React.ReactNode
    position?: 'left' | 'right'
  }
}

// Mock data fetching functions - in real app these would call APIs
export async function fetchNotifications(): Promise<NotificationItem[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100))
  
  return [
    {
      id: 'notif-1',
      title: 'New order received',
      message: 'Order #12345 has been placed',
      timestamp: '2 minutes ago',
      read: false,
      type: 'info'
    },
    {
      id: 'notif-2', 
      title: 'Payment processed',
      message: 'Payment of $299.99 completed',
      timestamp: '5 minutes ago',
      read: false,
      type: 'success'
    },
    {
      id: 'notif-3',
      title: 'Low stock alert',
      message: 'Product inventory is running low',
      timestamp: '10 minutes ago',
      read: true,
      type: 'warning'
    }
  ]
}

export async function fetchMessages(): Promise<MessageItem[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100))
  
  return [
    {
      id: 'msg-1',
      from: 'John Doe',
      subject: 'Project Update',
      preview: 'Hey, how\'s the project going?',
      timestamp: '1 hour ago',
      read: false,
      important: true
    },
    {
      id: 'msg-2',
      from: 'Jane Smith', 
      subject: 'Meeting Request',
      preview: 'Can we schedule a meeting?',
      timestamp: '2 hours ago',
      read: false,
      important: false
    }
  ]
}

export function createNotificationDropdownContent(
  notifications: NotificationItem[],
  onViewAll?: () => void
): React.ReactNode {
  const unreadCount = notifications.filter(n => !n.read).length
  
  return (
    <>
      <div className="px-3 py-2 border-b border-border">
        <div className="font-semibold">Notifications</div>
        <div className="text-xs text-muted-foreground">
          You have {unreadCount} unread notifications
        </div>
      </div>
      <div className="p-1">
        {notifications.slice(0, 3).map((notification) => (
          <button
            key={notification.id}
            type="button"
            className="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none transition-colors duration-200"
          >
            <div className="flex flex-col gap-1">
              <div className="font-medium">{notification.title}</div>
              <div className="text-xs text-muted-foreground">{notification.timestamp}</div>
            </div>
          </button>
        ))}
        <div className="h-px bg-border my-1" />
        <button
          type="button"
          onClick={onViewAll}
          className="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none transition-colors duration-200"
        >
          <div className="text-center text-sm text-primary">View all notifications</div>
        </button>
      </div>
    </>
  )
}

export function createMessageDropdownContent(
  messages: MessageItem[],
  onViewAll?: () => void
): React.ReactNode {
  const unreadCount = messages.filter(m => !m.read).length
  
  return (
    <>
      <div className="px-3 py-2 border-b border-border">
        <div className="font-semibold">Messages</div>
        <div className="text-xs text-muted-foreground">
          {unreadCount} new messages
        </div>
      </div>
      <div className="p-1">
        {messages.slice(0, 3).map((message) => (
          <button
            key={message.id}
            type="button"
            className="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none transition-colors duration-200"
          >
            <div className="flex flex-col gap-1">
              <div className="font-medium">{message.from}</div>
              <div className="text-xs text-muted-foreground">{message.preview}</div>
            </div>
          </button>
        ))}
        <div className="h-px bg-border my-1" />
        <button
          type="button"
          onClick={onViewAll}
          className="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none transition-colors duration-200"
        >
          <div className="text-center text-sm text-primary">View all messages</div>
        </button>
      </div>
    </>
  )
}

// Default header actions configuration
export function createDefaultHeaderActions(
  notifications: NotificationItem[],
  messages: MessageItem[],
  callbacks?: {
    onSearch?: () => void
    onNotificationsView?: () => void
    onMessagesView?: () => void
    onSettings?: () => void
    onProfile?: () => void
  }
): HeaderAction[] {
  const unreadNotifications = notifications.filter(n => !n.read).length
  const unreadMessages = messages.filter(m => !m.read).length
  
  return [
    {
      id: 'search',
      type: 'icon',
      icon: Search,
      label: 'Search',
      onClick: callbacks?.onSearch
    },
    {
      id: 'notifications',
      type: 'dropdown',
      icon: Bell,
      label: 'Notifications',
      badge: unreadNotifications > 0 ? {
        count: unreadNotifications,
        color: 'red',
        blink: true,
        blinkInterval: 2000
      } : undefined,
      dropdown: {
        content: createNotificationDropdownContent(notifications, callbacks?.onNotificationsView),
        position: 'right'
      }
    },
    {
      id: 'messages',
      type: 'dropdown', 
      icon: MessageSquare,
      label: 'Messages',
      badge: unreadMessages > 0 ? {
        count: unreadMessages,
        color: 'blue'
      } : undefined,
      dropdown: {
        content: createMessageDropdownContent(messages, callbacks?.onMessagesView),
        position: 'right'
      }
    },
    {
      id: 'mail',
      type: 'icon',
      icon: Mail,
      label: 'Mail',
      onClick: () => console.log('Mail clicked')
    },
    {
      id: 'settings',
      type: 'icon',
      icon: Settings,
      label: 'Settings',
      onClick: callbacks?.onSettings
    },
    {
      id: 'profile',
      type: 'icon',
      icon: User,
      label: 'User Profile',
      onClick: callbacks?.onProfile
    }
  ]
}
