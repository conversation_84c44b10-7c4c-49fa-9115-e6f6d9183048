import type { 
  ApiResponse, 
  DashboardStats, 
  Sale, 
  Order, 
  Product, 
  ChartDataPoint,
  ApiError 
} from './types'

// Import JSON data
import statsData from '../../api/dashboard/stats.json'
import salesData from '../../api/dashboard/sales.json'
import ordersData from '../../api/dashboard/orders.json'
import productsData from '../../api/dashboard/products.json'
import chartData from '../../api/dashboard/chart.json'

// Simulate network delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Simulate API response wrapper
const createApiResponse = <T>(data: T, status = 200, message = 'Success'): ApiResponse<T> => ({
  data,
  status,
  message
})

// Simulate potential API errors
const shouldSimulateError = () => Math.random() < 0.05 // 5% chance of error

const createApiError = (message: string, status = 500): ApiError => ({
  message,
  status
})

// Dashboard Stats API
export const getDashboardStats = async (): Promise<ApiResponse<DashboardStats>> => {
  await delay(Math.random() * 500 + 200) // 200-700ms delay
  
  if (shouldSimulateError()) {
    throw createApiError('Failed to fetch dashboard stats', 500)
  }
  
  return createApiResponse(statsData as DashboardStats)
}

// Recent Sales API
export const getRecentSales = async (): Promise<ApiResponse<Sale[]>> => {
  await delay(Math.random() * 400 + 150) // 150-550ms delay
  
  if (shouldSimulateError()) {
    throw createApiError('Failed to fetch recent sales', 500)
  }
  
  return createApiResponse(salesData as Sale[])
}

// Recent Orders API
export const getRecentOrders = async (): Promise<ApiResponse<Order[]>> => {
  await delay(Math.random() * 600 + 100) // 100-700ms delay
  
  if (shouldSimulateError()) {
    throw createApiError('Failed to fetch recent orders', 500)
  }
  
  return createApiResponse(ordersData as Order[])
}

// Top Products API
export const getTopProducts = async (): Promise<ApiResponse<Product[]>> => {
  await delay(Math.random() * 300 + 200) // 200-500ms delay
  
  if (shouldSimulateError()) {
    throw createApiError('Failed to fetch top products', 500)
  }
  
  return createApiResponse(productsData as Product[])
}

// Chart Data API
export const getChartData = async (): Promise<ApiResponse<ChartDataPoint[]>> => {
  await delay(Math.random() * 800 + 300) // 300-1100ms delay
  
  if (shouldSimulateError()) {
    throw createApiError('Failed to fetch chart data', 500)
  }
  
  return createApiResponse(chartData as ChartDataPoint[])
}

// Combined Dashboard Data API (for loading all data at once)
export const getDashboardData = async () => {
  try {
    const [stats, sales, orders, products, chart] = await Promise.all([
      getDashboardStats(),
      getRecentSales(),
      getRecentOrders(),
      getTopProducts(),
      getChartData()
    ])
    
    return {
      stats: stats.data,
      sales: sales.data,
      orders: orders.data,
      products: products.data,
      chart: chart.data
    }
  } catch (error) {
    throw error
  }
}
