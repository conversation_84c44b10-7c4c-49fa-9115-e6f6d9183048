import type { Route } from "./+types/dashboard";
import { AppSidebar } from "~/components/base/app-sidebar"
import { DashboardHeader } from "~/components/base/dashboard-header"
import { SidebarInset, SidebarProvider } from "~/components/ui/sidebar"
import { StatsCards } from "~/components/dashboard/stats-cards"
import { OverviewChart } from "~/components/dashboard/overview-chart"
import { RecentSales } from "~/components/dashboard/recent-sales"
import { RecentOrdersTable } from "~/components/dashboard/recent-orders-table"
import { TopProducts } from "~/components/dashboard/top-products"

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Dashboard - Admin Panel" },
    { name: "description", content: "Admin dashboard with analytics and metrics" },
  ];
}

export default function Dashboard() {
  const breadcrumbItems = [
    { title: "Home", href: "/" },
    { title: "Dashboard", isCurrentPage: true }
  ];

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <DashboardHeader
          title="Dashboard"
          breadcrumbItems={breadcrumbItems}
        />
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {/* Stats Cards */}
          <StatsCards />

          {/* Charts and Tables Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <OverviewChart />
            <RecentSales />
          </div>

          {/* Recent Orders Table */}
          <RecentOrdersTable />

          {/* Top Products */}
          <TopProducts />
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
