import type { Route } from "./+types/dashboard";
import { AppSidebar } from "~/components/base/app-sidebar"
import { DashboardHeader } from "~/components/base/dashboard-header"
import { SidebarInset, SidebarProvider } from "~/components/ui/sidebar"
import { StatsCards } from "~/dashboard/stats-cards"
import { OverviewChart } from "~/dashboard/overview-chart"
import { RecentSales } from "~/dashboard/recent-sales"
import { RecentOrdersTable } from "~/dashboard/recent-orders-table"
import { TopProducts } from "~/dashboard/top-products"

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Dashboard - Admin Panel" },
    { name: "description", content: "Admin dashboard with analytics and metrics" },
  ];
}

export default function Dashboard() {
  const breadcrumbItems = [
    { title: "Home", href: "/" },
    { title: "Dashboard", isCurrentPage: true }
  ];

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <DashboardHeader
          title="Dashboard"
          breadcrumbItems={breadcrumbItems}
        />
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {/* Stats Cards */}
          <StatsCards />

          {/* Charts and Tables Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <OverviewChart />
            <RecentSales />
          </div>

          {/* Recent Orders Table */}
          <RecentOrdersTable />

          {/* Top Products */}
          <TopProducts />
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
