import type { Route } from "./+types/dashboard";
import { AppLayout } from "~/components/layouts/app-layout"
import { StatsCards } from "~/dashboard/stats-cards"
import { OverviewChart } from "~/dashboard/overview-chart"
import { RecentSales } from "~/dashboard/recent-sales"
import { RecentOrdersTable } from "~/dashboard/recent-orders-table"
import { TopProducts } from "~/dashboard/top-products"

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Dashboard - Admin Panel" },
    { name: "description", content: "Admin dashboard with analytics and metrics" },
  ];
}

export default function Dashboard() {
  const breadcrumbItems = [
    { title: "Home", href: "/" },
    { title: "Dashboard", isCurrentPage: true }
  ];

  return (
    <AppLayout
      title="Dashboard"
      breadcrumbItems={breadcrumbItems}
      headerOptions={{
        enableNotifications: true,
        enableMessages: true,
        refreshInterval: 30000,
        callbacks: {
          onSearch: () => console.log("Search clicked"),
          onNotificationsView: () => console.log("View all notifications"),
          onMessagesView: () => console.log("View all messages"),
          onSettings: () => console.log("Settings clicked"),
          onProfile: () => console.log("Profile clicked")
        }
      }}
    >
      {/* Stats Cards */}
      <StatsCards />

      {/* Charts and Tables Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <OverviewChart />
        <RecentSales />
      </div>

      {/* Recent Orders Table */}
      <RecentOrdersTable />

      {/* Top Products */}
      <TopProducts />
    </AppLayout>
  )
}
