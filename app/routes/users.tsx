import type { Route } from "./+types/users";
import { AppLayout } from "~/components/layouts/app-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Avatar } from "~/components/ui/avatar";
import { Users, UserPlus, UserCheck, UserX, Mail, Phone, MapPin } from "lucide-react";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Users - Admin Panel" },
    { name: "description", content: "Manage users and user accounts" },
  ];
}

export default function UsersPage() {
  const breadcrumbItems = [
    { title: "Home", href: "/" },
    { title: "Users", isCurrentPage: true }
  ];

  const users = [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+****************",
      location: "New York, NY",
      status: "active",
      role: "Admin",
      lastLogin: "2 hours ago"
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+****************",
      location: "Los Angeles, CA",
      status: "active",
      role: "User",
      lastLogin: "1 day ago"
    },
    {
      id: 3,
      name: "Mike Johnson",
      email: "<EMAIL>",
      phone: "+****************",
      location: "Chicago, IL",
      status: "inactive",
      role: "User",
      lastLogin: "1 week ago"
    },
    {
      id: 4,
      name: "Sarah Wilson",
      email: "<EMAIL>",
      phone: "+****************",
      location: "Miami, FL",
      status: "pending",
      role: "User",
      lastLogin: "Never"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Active</Badge>;
      case "inactive":
        return <Badge variant="secondary">Inactive</Badge>;
      case "pending":
        return <Badge className="bg-yellow-500">Pending</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <AppLayout
      title="Users"
      breadcrumbItems={breadcrumbItems}
      className="gap-6 p-6 pt-0"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
          <p className="text-muted-foreground">
            Manage user accounts, permissions, and access controls.
          </p>
        </div>
        <Button>
          <UserPlus className="mr-2 h-4 w-4" />
          Add User
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,234</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,180</div>
            <p className="text-xs text-muted-foreground">
              95.6% of total users
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Users</CardTitle>
            <UserX className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">54</div>
            <p className="text-xs text-muted-foreground">
              Awaiting approval
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New This Month</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">89</div>
            <p className="text-xs text-muted-foreground">
              +23% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Users List */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Users</CardTitle>
          <CardDescription>
            A list of recent user registrations and account updates.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <Avatar className="h-10 w-10">
                    <div className="flex items-center justify-center h-full w-full bg-primary text-primary-foreground text-sm font-medium">
                      {user.name.split(' ').map(n => n[0]).join('')}
                    </div>
                  </Avatar>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{user.name}</h4>
                      {getStatusBadge(user.status)}
                      <Badge variant="outline">{user.role}</Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        {user.email}
                      </div>
                      <div className="flex items-center gap-1">
                        <Phone className="h-3 w-3" />
                        {user.phone}
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {user.location}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">Last Login</div>
                  <div className="text-xs text-muted-foreground">{user.lastLogin}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </AppLayout>
  );
}
