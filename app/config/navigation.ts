import { type RouteConfig } from "@react-router/dev/routes";
import { Home, SquareTerminal, Users, BarChart3, FileText, Bell, Smartphone, Settings2 } from "lucide-react";

// Definição unificada para rotas e navegação
export const navigationConfig = [
  {
    title: "Home",
    url: "/",
    icon: Home,
    component: "pages/dashboard/home.tsx",
    isIndex: true,
    isActive: false
  },
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: SquareTerminal,
    component: "pages/dashboard/dashboard.tsx",
    isActive: true,
    items: [
      {
        title: "Overview",
        url: "/dashboard",
        component: "pages/dashboard/dashboard.tsx"
      },
      {
        title: "Analytics",
        url: "/analytics",
        component: "pages/dashboard/analytics.tsx"
      },
      // ...outros subitens
    ]
  },
  // ...outros itens
];

// Gerar configuração de rotas para React Router
export const generateRouteConfig = (): RouteConfig => {
  const routes = [];
  
  // Processar itens de navegação para criar rotas
  navigationConfig.forEach(item => {
    if (item.isIndex) {
      routes.push({ type: "index", path: item.component });
    } else {
      routes.push({ type: "route", path: item.url, component: item.component });
    }
    
    // Processar subitens
    if (item.items) {
      item.items.forEach(subItem => {
        routes.push({ type: "route", path: subItem.url, component: subItem.component });
      });
    }
  });
  
  // Adicionar rota 404
  routes.push({ type: "route", path: "*", component: "pages/404.tsx" });
  
  return routes;
};

// Gerar configuração de navegação para o sidebar
export const generateSidebarConfig = () => {
  return navigationConfig.map(item => ({
    title: item.title,
    url: item.url,
    icon: item.icon.name,
    isActive: item.isActive,
    items: item.items ? item.items.map(subItem => ({
      title: subItem.title,
      url: subItem.url
    })) : undefined
  }));
};