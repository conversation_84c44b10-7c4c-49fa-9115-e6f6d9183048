import { Home, SquareTerminal, Users, Bell, Smartphone, Settings2 } from "lucide-react";

// Definição unificada para rotas e navegação
export const navigationConfig = [
  {
    title: "Home",
    url: "/",
    icon: Home,
    component: "pages/dashboard/home.tsx",
    isIndex: true,
    isActive: false
  },
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: SquareTerminal,
    component: "pages/dashboard/dashboard.tsx",
    isActive: true,
    items: [
      {
        title: "Overview",
        url: "/dashboard",
        component: "pages/dashboard/dashboard.tsx"
      },
      {
        title: "Analytics",
        url: "/analytics",
        component: "pages/dashboard/analytics.tsx"
      },
      {
        title: "Reports",
        url: "/reports",
        component: "pages/dashboard/reports.tsx"
      }
    ]
  },
  {
    title: "Users",
    url: "/users",
    icon: Users,
    component: "pages/admin/users.tsx",
    isActive: false,
    badge: {
      count: 3,
      color: "blue",
      text: "New"
    }
  },
  {
    title: "Notifications",
    url: "/notifications",
    icon: Bell,
    component: "pages/admin/notifications.tsx",
    isActive: false,
    badge: {
      count: 5,
      color: "red",
      blink: true
    }
  },
  {
    title: "PWA Features",
    url: "/pwa",
    icon: Smartphone,
    component: "components/base/pwa/pwa-page.tsx",
    isActive: false,
    items: [
      {
        title: "PWA Status",
        url: "/pwa",
        component: "components/base/pwa/pwa-page.tsx"
      },
      {
        title: "PWA Demo",
        url: "/pwa-demo",
        component: "components/base/pwa/pwa-demo-page.tsx"
      }
    ]
  },
  {
    title: "Settings",
    url: "/settings",
    icon: Settings2,
    component: "pages/admin/settings.tsx",
    isActive: false,
    items: [
      {
        title: "General",
        url: "/settings/general",
        component: "pages/admin/settings.tsx"
      },
      {
        title: "Security",
        url: "/settings/security",
        component: "pages/admin/settings.tsx"
      },
      {
        title: "Integrations",
        url: "/settings/integrations",
        component: "pages/admin/settings.tsx"
      }
    ]
  }
];

// Gerar configuração de rotas para React Router
export const generateRouteConfig = () => {
  const routes = [];

  // Processar itens de navegação para criar rotas
  navigationConfig.forEach(item => {
    if (item.isIndex) {
      routes.push({
        type: "index",
        file: item.component
      });
    } else {
      routes.push({
        type: "route",
        path: item.url,
        file: item.component
      });
    }

    // Processar subitens
    if (item.items) {
      item.items.forEach(subItem => {
        // Evitar duplicatas - só adicionar se a URL for diferente do item pai
        if (subItem.url !== item.url) {
          routes.push({
            type: "route",
            path: subItem.url,
            file: subItem.component
          });
        }
      });
    }
  });

  // Adicionar rota 404
  routes.push({
    type: "route",
    path: "*",
    file: "pages/404.tsx"
  });

  return routes;
};

// Gerar configuração de navegação para o sidebar
export const generateSidebarConfig = () => {
  return navigationConfig.map(item => ({
    title: item.title,
    url: item.url,
    icon: item.icon.name || 'Home', // fallback icon
    isActive: item.isActive,
    badge: item.badge,
    items: item.items ? item.items.map(subItem => ({
      title: subItem.title,
      url: subItem.url
    })) : undefined
  }));
};

// Helper para encontrar rota por URL
export const findRouteByUrl = (url: string) => {
  for (const item of navigationConfig) {
    if (item.url === url) {
      return item;
    }
    if (item.items) {
      const subItem = item.items.find(sub => sub.url === url);
      if (subItem) {
        return { ...subItem, parent: item };
      }
    }
  }
  return null;
};

// Helper para gerar breadcrumbs
export const generateBreadcrumbs = (url: string) => {
  const route = findRouteByUrl(url);
  if (!route) return [];

  const breadcrumbs = [{ title: "Home", href: "/" }];

  if ('parent' in route && route.parent) {
    breadcrumbs.push({
      title: route.parent.title,
      href: route.parent.url
    });
  }

  breadcrumbs.push({
    title: route.title,
    href: route.url,
    isCurrentPage: true
  } as any);

  return breadcrumbs;
};