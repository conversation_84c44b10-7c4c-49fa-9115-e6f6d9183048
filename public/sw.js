// Service Worker for PWA functionality
const CACHE_NAME = 'admin-dashboard-v1'
const STATIC_CACHE_NAME = 'admin-dashboard-static-v1'
const DYNAMIC_CACHE_NAME = 'admin-dashboard-dynamic-v1'

// Files to cache on install
const STATIC_FILES = [
  '/',
  '/dashboard',
  '/pwa',
  '/users',
  '/settings',
  '/analytics',
  '/reports',
  '/notifications',
  '/manifest.json',
  '/icon-192x192.png',
  '/icon-512x512.png',
  '/offline.html'
]

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Caching static files')
        return cache.addAll(STATIC_FILES)
      })
      .then(() => {
        console.log('Service Worker: Static files cached')
        return self.skipWaiting()
      })
      .catch(error => {
        console.error('Service Worker: Failed to cache static files', error)
      })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...')
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== STATIC_CACHE_NAME && cacheName !== DYNAMIC_CACHE_NAME) {
              console.log('Service Worker: Deleting old cache', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('Service Worker: Activated')
        return self.clients.claim()
      })
  )
})

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }

  // Skip external requests
  if (url.origin !== location.origin) {
    return
  }

  // Handle API requests with network-first strategy
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(
      fetch(request)
        .then(response => {
          // Clone response for caching
          const responseClone = response.clone()
          
          // Cache successful responses
          if (response.status === 200) {
            caches.open(DYNAMIC_CACHE_NAME)
              .then(cache => cache.put(request, responseClone))
          }
          
          return response
        })
        .catch(() => {
          // Fallback to cache if network fails
          return caches.match(request)
            .then(cachedResponse => {
              if (cachedResponse) {
                return cachedResponse
              }
              
              // Return offline fallback for API requests
              return new Response(
                JSON.stringify({ 
                  error: 'Offline', 
                  message: 'This data is not available offline' 
                }),
                {
                  status: 503,
                  statusText: 'Service Unavailable',
                  headers: { 'Content-Type': 'application/json' }
                }
              )
            })
        })
    )
    return
  }

  // Handle page requests with cache-first strategy
  event.respondWith(
    caches.match(request)
      .then(cachedResponse => {
        if (cachedResponse) {
          // Serve from cache
          return cachedResponse
        }
        
        // Fetch from network and cache
        return fetch(request)
          .then(response => {
            // Don't cache non-successful responses
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response
            }
            
            // Clone response for caching
            const responseClone = response.clone()
            
            // Cache the response
            caches.open(DYNAMIC_CACHE_NAME)
              .then(cache => cache.put(request, responseClone))
            
            return response
          })
          .catch(() => {
            // Return offline page for navigation requests
            if (request.mode === 'navigate') {
              return caches.match('/offline.html')
                .then(cachedResponse => {
                  if (cachedResponse) {
                    return cachedResponse
                  }
                  
                  // Fallback offline page
                  return new Response(
                    `
                    <!DOCTYPE html>
                    <html>
                    <head>
                      <title>Offline - Admin Dashboard</title>
                      <meta name="viewport" content="width=device-width, initial-scale=1">
                      <style>
                        body { 
                          font-family: system-ui, sans-serif; 
                          text-align: center; 
                          padding: 2rem; 
                          background: #f8fafc;
                        }
                        .container { 
                          max-width: 400px; 
                          margin: 0 auto; 
                          background: white; 
                          padding: 2rem; 
                          border-radius: 8px; 
                          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                        }
                        h1 { color: #1f2937; margin-bottom: 1rem; }
                        p { color: #6b7280; margin-bottom: 1.5rem; }
                        button { 
                          background: #3b82f6; 
                          color: white; 
                          border: none; 
                          padding: 0.75rem 1.5rem; 
                          border-radius: 6px; 
                          cursor: pointer; 
                          font-size: 1rem;
                        }
                        button:hover { background: #2563eb; }
                      </style>
                    </head>
                    <body>
                      <div class="container">
                        <h1>You're Offline</h1>
                        <p>Please check your internet connection and try again.</p>
                        <button onclick="window.location.reload()">Try Again</button>
                      </div>
                    </body>
                    </html>
                    `,
                    {
                      status: 200,
                      statusText: 'OK',
                      headers: { 'Content-Type': 'text/html' }
                    }
                  )
                })
            }
            
            throw error
          })
      })
  )
})

// Handle messages from the main thread
self.addEventListener('message', (event) => {
  const { data } = event
  
  if (data && data.type === 'SKIP_WAITING') {
    console.log('Service Worker: Received SKIP_WAITING message')
    self.skipWaiting()
  }
  
  if (data && data.type === 'GET_VERSION') {
    event.ports[0].postMessage({
      type: 'VERSION',
      version: CACHE_NAME
    })
  }
})

// Background sync (if supported)
if ('sync' in self.registration) {
  self.addEventListener('sync', (event) => {
    console.log('Service Worker: Background sync triggered', event.tag)
    
    if (event.tag === 'background-sync') {
      event.waitUntil(
        // Perform background sync tasks
        Promise.resolve()
          .then(() => {
            console.log('Service Worker: Background sync completed')
          })
          .catch(error => {
            console.error('Service Worker: Background sync failed', error)
          })
      )
    }
  })
}

// Push notifications (if supported)
if ('push' in self.registration) {
  self.addEventListener('push', (event) => {
    console.log('Service Worker: Push message received', event)
    
    const options = {
      body: event.data ? event.data.text() : 'New notification',
      icon: '/icon-192x192.png',
      badge: '/icon-72x72.png',
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: 1
      },
      actions: [
        {
          action: 'explore',
          title: 'View',
          icon: '/icon-192x192.png'
        },
        {
          action: 'close',
          title: 'Close',
          icon: '/icon-192x192.png'
        }
      ]
    }
    
    event.waitUntil(
      self.registration.showNotification('Admin Dashboard', options)
    )
  })
  
  self.addEventListener('notificationclick', (event) => {
    console.log('Service Worker: Notification clicked', event)
    
    event.notification.close()
    
    if (event.action === 'explore') {
      event.waitUntil(
        clients.openWindow('/')
      )
    }
  })
}

console.log('Service Worker: Script loaded')
